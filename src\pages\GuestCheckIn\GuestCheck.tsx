import React, { useState, useEffect } from "react";
import { AlertCircle, CheckCircle, DollarSign, Users } from "lucide-react";
import { CheckInForm, GuestCheck } from "./Components/types/types";
import { CheckDetailsDialog } from "./Components/CheckDetailsModal";
import { CheckInDialog } from "./Components/CheckInDialogue";
import { GuestChecksHeader } from "./Components/GuestCheckHeader";
import { GuestChecksTabs } from "./Components/GuestCheckTabs";
import { GuestChecksSearch } from "./Components/SearchBar";
import { StatsCard } from "./Components/StatsCard";
import { Screen } from "@/app-components/layout/screen";
import { useGetGuestCheckQuery } from "@/redux/slices/guestCheck";

// Initial guest checks data (static, as a fallback)
const initialGuestChecks: GuestCheck[] = [
  {
    id: "CHK001",
    tableNumber: "T-05",
    guestCount: 4,
    waiterName: "<PERSON>",
    orderTime: "2025-07-16T14:30:00",
    status: "active",
    subtotal: 85.5,
    tax: 8.55,
    serviceCharge: 12.83,
    total: 106.88,
    items: [
      { name: "Grilled Chicken", price: 22.5, qty: 2 },
      { name: "Caesar Salad", price: 12.5, qty: 1 },
      { name: "Pasta Alfredo", price: 18.0, qty: 1 },
      { name: "Coca Cola", price: 3.5, qty: 4 },
    ],
    discounts: [{ type: "Happy Hour", amount: 5.0 }],
    voids: [],
  },
  {
    id: "CHK002",
    tableNumber: "T-12",
    guestCount: 2,
    waiterName: "Mike Chen",
    orderTime: "2025-07-16T15:15:00",
    status: "active",
    subtotal: 42.0,
    tax: 4.2,
    serviceCharge: 6.3,
    total: 52.5,
    items: [
      { name: "Margherita Pizza", price: 16.0, qty: 1 },
      { name: "Tiramisu", price: 8.0, qty: 2 },
      { name: "Espresso", price: 4.0, qty: 2 },
    ],
    discounts: [],
    voids: [],
  },
  {
    id: "CHK003",
    tableNumber: "T-03",
    guestCount: 6,
    waiterName: "Emma Wilson",
    orderTime: "2025-07-16T13:45:00",
    status: "closed",
    subtotal: 156.0,
    tax: 15.6,
    serviceCharge: 23.4,
    total: 195.0,
    items: [
      { name: "Ribeye Steak", price: 32.0, qty: 2 },
      { name: "Lobster Tail", price: 28.0, qty: 1 },
      { name: "Wine Bottle", price: 45.0, qty: 1 },
      { name: "Side Salad", price: 8.0, qty: 3 },
    ],
    discounts: [],
    voids: [],
  },
];

const GuestChecksUI: React.FC = () => {
  // Use the hook with proper error and loading handling
  const { data, isLoading, isError, error } = useGetGuestCheckQuery();

  // Log the query status for debugging
  console.log("Guest Check Query Status:", {
    data,
    isLoading,
    isError,
    error: isError ? error : null,
  });

  const [activeTab, setActiveTab] = useState("active");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCheck, setSelectedCheck] = useState<GuestCheck | null>(null);
  const [showCheckInDialog, setShowCheckInDialog] = useState(false);
  const [showCheckDetails, setShowCheckDetails] = useState(false);
  // Initialize with initialGuestChecks as fallback
  const [guestChecks, setGuestChecks] = useState<GuestCheck[]>(initialGuestChecks);
  const [checkInForm, setCheckInForm] = useState<CheckInForm>({
    tableNumber: "",
    guestCount: "",
    waiterName: "",
    specialRequests: "",
  });

  // Update guestChecks when data changes
  useEffect(() => {
    if (data) {
      console.log("Updating guestChecks with API data:", data);
      setGuestChecks(data);
    }
  }, [data]);

  // Log when loading or error occurs
  useEffect(() => {
    if (isLoading) {
      console.log("Guest Check Query is loading...");
    }
    if (isError) {
      console.error("Guest Check Query error:", error);
    }
  }, [isLoading, isError, error]);

  const activeChecks = guestChecks.filter((check) => check.status === "active");
  const closedChecks = guestChecks.filter((check) => check.status === "closed");

  const handleCloseCheck = (checkId: string) => {
    setGuestChecks((checks) =>
      checks.map((check) => (check.id === checkId ? { ...check, status: "closed" } : check))
    );
  };

  const handleVoidItem = (checkId: string, itemIndex: number) => {
    setGuestChecks((checks) =>
      checks.map((check) => {
        if (check.id === checkId) {
          const voidedItem = check.items[itemIndex];
          return {
            ...check,
            items: check.items.filter((_, index) => index !== itemIndex),
            voids: [...check.voids, voidedItem],
          };
        }
        return check;
      })
    );
  };

  const handleCheckIn = () => {
    const newCheck: GuestCheck = {
      id: `CHK${String(guestChecks.length + 1).padStart(3, "0")}`,
      tableNumber: checkInForm.tableNumber,
      guestCount: parseInt(checkInForm.guestCount),
      waiterName: checkInForm.waiterName,
      orderTime: new Date().toISOString(),
      status: "active",
      subtotal: 0,
      tax: 0,
      serviceCharge: 0,
      total: 0,
      items: [],
      discounts: [],
      voids: [],
    };
    setGuestChecks([...guestChecks, newCheck]);
    setCheckInForm({ tableNumber: "", guestCount: "", waiterName: "", specialRequests: "" });
    setShowCheckInDialog(false);
  };

  const totalRevenue = closedChecks.reduce((sum, check) => sum + check.total, 0).toFixed(2);
  const avgCheckSize =
    closedChecks.length > 0 ? (parseFloat(totalRevenue) / closedChecks.length).toFixed(2) : "0.00";

  return (
    <Screen>
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-white">
        <div className="w-full max-w-[1600px] mx-auto p-6">
          {isLoading && <div>Loading guest checks...</div>}
          {isError && <div>Error loading guest checks: {error?.message || "Unknown error"}</div>}
          <GuestChecksHeader onNewCheckIn={() => setShowCheckInDialog(true)} />
          <GuestChecksSearch searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatsCard
              title="Active Checks"
              value={activeChecks.length}
              icon={<AlertCircle className="h-6 w-6 text-primary" />}
              trend="+12% from yesterday"
            />
            <StatsCard
              title="Closed Today"
              value={closedChecks.length}
              icon={<CheckCircle className="h-6 w-6 text-secondary" />}
              trend="+8% from yesterday"
            />
            <StatsCard
              title="Total Revenue"
              value={`$${totalRevenue}`}
              icon={<DollarSign className="h-6 w-6 text-primary" />}
              trend="+15% from yesterday"
            />
            <StatsCard
              title="Avg Check Size"
              value={`$${avgCheckSize}`}
              icon={<Users className="h-6 w-6 text-secondary" />}
              trend="+5% from yesterday"
            />
          </div>
          <GuestChecksTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            activeChecks={activeChecks}
            closedChecks={closedChecks}
            onCloseCheck={handleCloseCheck}
            onVoidItem={handleVoidItem}
            onSplitCheck={(check) => setSelectedCheck(check)}
            onViewCheck={(check) => {
              setSelectedCheck(check);
              setShowCheckDetails(true);
            }}
          />
          <CheckInDialog
            open={showCheckInDialog}
            onOpenChange={setShowCheckInDialog}
            checkInForm={checkInForm}
            setCheckInForm={setCheckInForm}
            onCheckIn={handleCheckIn}
          />
          <CheckDetailsDialog
            open={showCheckDetails}
            onOpenChange={setShowCheckDetails}
            selectedCheck={selectedCheck}
          />
        </div>
      </div>
    </Screen>
  );
};

export default GuestChecksUI;