import { RFQ } from "@/types/procurement";

export interface RFQEmailTemplate {
  subject: string;
  body: string;
  isHTML?: boolean;
}

export interface RFQEmailOptions {
  to: string[];
  cc?: string[];
  bcc?: string[];
  attachPDF?: boolean;
  customMessage?: string;
  template?: 'standard' | 'urgent' | 'reminder' | 'cancellation';
}

export const generateRFQEmailTemplate = (
  rfq: RFQ,
  template: 'standard' | 'urgent' | 'reminder' | 'cancellation' = 'standard',
  customMessage?: string
): RFQEmailTemplate => {
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string | undefined) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const companyName = "GMC Company";
  const companyEmail = "<EMAIL>";
  const companyPhone = "+****************";

  const templates = {
    standard: {
      subject: `Request for Quotation ${rfq.rfq_number} - ${companyName}`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .rfq-details { background: #fff; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 15px 0; }
            .rfq-number { font-size: 18px; font-weight: bold; color: #007bff; }
            .highlight { background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
            table { width: 100%; border-collapse: collapse; margin: 15px 0; }
            th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
            th { background: #f8f9fa; font-weight: bold; }
            .deadline-notice { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Request for Quotation</h2>
            <p>Dear Supplier,</p>
            <p>We are pleased to invite you to submit a quotation for the following requirements.</p>
          </div>
          
          <div class="content">
            ${customMessage ? `
            <div class="highlight">
              <strong>Special Instructions:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <div class="rfq-details">
              <h3>RFQ Details</h3>
              <p><strong>RFQ Number:</strong> <span class="rfq-number">${rfq.rfq_number}</span></p>
              <p><strong>Date:</strong> ${formatDate(rfq.created_at)}</p>
              <p><strong>Response Deadline:</strong> <strong style="color: #dc3545;">${formatDateTime(rfq.response_deadline)}</strong></p>
              <p><strong>Delivery Location:</strong> ${rfq.delivery_location_name}</p>
              ${rfq.delivery_address ? `<p><strong>Delivery Address:</strong> ${rfq.delivery_address}</p>` : ''}
              <p><strong>Required Date:</strong> ${formatDate(rfq.required_date)}</p>
            </div>
            
            <div class="deadline-notice">
              <strong>⏰ Important:</strong> Please submit your quotation by ${formatDateTime(rfq.response_deadline)}
            </div>
            
            <h3>Items Required</h3>
            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th>Quantity</th>
                  <th>Unit</th>
                  <th>Specifications</th>
                  <th>Est. Unit Cost</th>
                </tr>
              </thead>
              <tbody>
                ${rfq.items?.map(item => `
                  <tr>
                    <td>
                      <strong>${item.product_name || 'N/A'}</strong>
                      ${item.product_code ? `<br><small>${item.product_code}</small>` : ''}
                    </td>
                    <td>${item.quantity || 0}</td>
                    <td>${item.unit_of_measure_name || 'N/A'}</td>
                    <td>${item.specifications || '-'}</td>
                    <td>${item.estimated_unit_cost ? `$${item.estimated_unit_cost.toLocaleString()}` : '-'}</td>
                  </tr>
                `).join('') || '<tr><td colspan="5">No items</td></tr>'}
              </tbody>
            </table>
            
            ${rfq.notes ? `
            <div class="rfq-details">
              <h3>Additional Notes</h3>
              <p>${rfq.notes.replace(/\n/g, '<br>')}</p>
            </div>
            ` : ''}
            
            ${rfq.terms_and_conditions ? `
            <div class="rfq-details">
              <h3>Terms and Conditions</h3>
              <p>${rfq.terms_and_conditions.replace(/\n/g, '<br>')}</p>
            </div>
            ` : ''}
            
            <div class="highlight">
              <p><strong>Quotation Requirements:</strong></p>
              <ul>
                <li>Provide unit prices for all items</li>
                <li>Include delivery timeframes</li>
                <li>Specify payment terms</li>
                <li>Include any relevant certifications or warranties</li>
                <li>Submit before the deadline specified above</li>
              </ul>
            </div>
          </div>
          
          <div class="footer">
            <p><strong>Contact Information:</strong></p>
            <p>${companyName}<br>
            Email: ${companyEmail}<br>
            Phone: ${companyPhone}</p>
            <p><em>Thank you for your participation in our procurement process.</em></p>
          </div>
        </body>
        </html>
      `
    },
    
    urgent: {
      subject: `🚨 URGENT RFQ ${rfq.rfq_number} - Immediate Response Required`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .urgent-header { background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .rfq-details { background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 15px; margin: 15px 0; }
            .rfq-number { font-size: 18px; font-weight: bold; color: #dc3545; }
            .urgent-notice { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="urgent-header">
            <h2>🚨 URGENT REQUEST FOR QUOTATION</h2>
            <p>This RFQ requires immediate attention and expedited response.</p>
          </div>
          
          <div class="content">
            <div class="urgent-notice">
              <strong>⚠️ URGENT RESPONSE REQUIRED</strong><br>
              This RFQ has been marked as urgent. Please prioritize and respond within the shortest possible time.
            </div>
            
            ${customMessage ? `
            <div class="urgent-notice">
              <strong>Special Instructions:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <div class="rfq-details">
              <h3>Urgent RFQ Details</h3>
              <p><strong>RFQ Number:</strong> <span class="rfq-number">${rfq.rfq_number}</span></p>
              <p><strong>Response Deadline:</strong> <strong style="color: #dc3545;">${formatDateTime(rfq.response_deadline)}</strong></p>
              <p><strong>Required Date:</strong> <strong style="color: #dc3545;">${formatDate(rfq.required_date)}</strong></p>
            </div>
            
            <div class="urgent-notice">
              <p><strong>Immediate Action Required:</strong></p>
              <ul>
                <li>Respond within the deadline specified above</li>
                <li>Contact us immediately if you cannot meet the deadline</li>
                <li>Call ${companyPhone} for urgent clarifications</li>
                <li>Prioritize this RFQ in your response queue</li>
              </ul>
            </div>
          </div>
          
          <div class="footer">
            <p><strong>Emergency Contact:</strong> ${companyPhone}</p>
            <p><strong>Email:</strong> ${companyEmail}</p>
          </div>
        </body>
        </html>
      `
    },
    
    reminder: {
      subject: `Reminder: RFQ ${rfq.rfq_number} Response Due Soon`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .reminder-header { background: #ffc107; color: #212529; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .reminder-notice { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="reminder-header">
            <h2>⏰ RFQ Response Reminder</h2>
            <p>This is a friendly reminder about RFQ ${rfq.rfq_number} with an approaching deadline.</p>
          </div>
          
          <div class="content">
            <div class="reminder-notice">
              <strong>⚠️ Response Deadline Approaching</strong><br>
              Your response to RFQ ${rfq.rfq_number} is due by ${formatDateTime(rfq.response_deadline)}.
            </div>
            
            ${customMessage ? `
            <div class="reminder-notice">
              <strong>Additional Message:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <p>If you have already submitted your response, please disregard this reminder.</p>
            <p>If you need additional time or have questions, please contact us immediately.</p>
          </div>
          
          <div class="footer">
            <p><strong>Contact:</strong> ${companyEmail} | ${companyPhone}</p>
          </div>
        </body>
        </html>
      `
    },
    
    cancellation: {
      subject: `RFQ ${rfq.rfq_number} - CANCELLED`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .cancellation-header { background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .cancellation-notice { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="cancellation-header">
            <h2>❌ RFQ CANCELLED</h2>
            <p>RFQ ${rfq.rfq_number} has been cancelled.</p>
          </div>
          
          <div class="content">
            <div class="cancellation-notice">
              <strong>⚠️ RFQ CANCELLATION NOTICE</strong><br>
              This RFQ has been cancelled. Please do not submit any quotations for this request.
            </div>
            
            ${customMessage ? `
            <div class="cancellation-notice">
              <strong>Cancellation Reason:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <p>We apologize for any inconvenience this may cause.</p>
            <p>If you have any questions, please contact us at ${companyPhone}.</p>
          </div>
          
          <div class="footer">
            <p><strong>Contact:</strong> ${companyEmail} | ${companyPhone}</p>
          </div>
        </body>
        </html>
      `
    }
  };

  return {
    ...templates[template],
    isHTML: true
  };
};

export const createRFQEmailData = (
  rfq: RFQ,
  options: RFQEmailOptions
): any => {
  const template = generateRFQEmailTemplate(
    rfq,
    options.template || 'standard',
    options.customMessage
  );

  return {
    to: options.to,
    cc: options.cc || [],
    bcc: options.bcc || [],
    subject: template.subject,
    body: template.body,
    isHTML: template.isHTML,
    attachments: options.attachPDF ? [
      {
        filename: `rfq-${rfq.rfq_number}.pdf`,
        contentType: 'application/pdf'
      }
    ] : []
  };
};

export const validateEmailAddresses = (emails: string[]): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emails.every(email => emailRegex.test(email.trim()));
};

export const getDefaultRFQEmailRecipients = (rfq: RFQ): string[] => {
  // This would typically pull supplier emails from the RFQ's selected suppliers
  // For now, return empty array as supplier emails would come from the backend
  return [];
};
