import { RFQ } from "@/types/procurement";

export interface RFQPDFOptions {
  includeHeader?: boolean;
  includeFooter?: boolean;
  companyLogo?: string;
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website?: string;
  };
}

export const generateRFQHTML = (
  rfq: RFQ,
  options: RFQPDFOptions = {}
): string => {
  const {
    includeHeader = true,
    includeFooter = true,
    companyInfo = {
      name: "GMC Company",
      address: "123 Business Street, City, State 12345",
      phone: "+****************",
      email: "<EMAIL>",
      website: "www.gmccompany.com"
    }
  } = options;

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string | undefined) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Request for Quotation ${rfq.rfq_number}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Arial', sans-serif;
          line-height: 1.6;
          color: #333;
          background: white;
        }
        
        .container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          border-bottom: 2px solid #e5e7eb;
          padding-bottom: 20px;
        }
        
        .company-info {
          flex: 1;
        }
        
        .company-name {
          font-size: 24px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 5px;
        }
        
        .company-details {
          font-size: 12px;
          color: #6b7280;
          line-height: 1.4;
        }
        
        .rfq-info {
          text-align: right;
          flex: 1;
        }
        
        .rfq-title {
          font-size: 28px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 10px;
        }
        
        .rfq-number {
          font-size: 18px;
          color: #3b82f6;
          font-weight: 600;
          margin-bottom: 5px;
        }
        
        .rfq-date {
          font-size: 12px;
          color: #6b7280;
        }
        
        .details-section {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
          margin-bottom: 30px;
        }
        
        .detail-card {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 20px;
          background: #f9fafb;
        }
        
        .detail-title {
          font-size: 16px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 15px;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }
        
        .detail-label {
          font-weight: 600;
          color: #4b5563;
          font-size: 14px;
        }
        
        .detail-value {
          color: #1f2937;
          font-size: 14px;
        }
        
        .suppliers-section {
          margin-bottom: 30px;
        }
        
        .suppliers-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
          margin-top: 15px;
        }
        
        .supplier-card {
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          padding: 15px;
          background: #f9fafb;
          text-align: center;
        }
        
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
        }
        
        .items-table th {
          background: #f3f4f6;
          padding: 12px;
          text-align: left;
          font-weight: 600;
          color: #1f2937;
          border-bottom: 1px solid #e5e7eb;
          font-size: 14px;
        }
        
        .items-table td {
          padding: 12px;
          border-bottom: 1px solid #f3f4f6;
          font-size: 14px;
          vertical-align: top;
        }
        
        .items-table tr:last-child td {
          border-bottom: none;
        }
        
        .items-table tr:nth-child(even) {
          background: #f9fafb;
        }
        
        .text-right {
          text-align: right;
        }
        
        .font-medium {
          font-weight: 600;
        }
        
        .notes-section {
          margin-top: 30px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }
        
        .notes-card {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 20px;
          background: #f9fafb;
        }
        
        .notes-title {
          font-size: 16px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 10px;
        }
        
        .notes-content {
          font-size: 14px;
          color: #4b5563;
          line-height: 1.6;
          white-space: pre-wrap;
        }
        
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          text-align: center;
          font-size: 12px;
          color: #6b7280;
        }
        
        .status-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
        }
        
        .status-open {
          background: #d1fae5;
          color: #065f46;
        }
        
        .status-closed {
          background: #dbeafe;
          color: #1e40af;
        }
        
        .status-cancelled {
          background: #fee2e2;
          color: #991b1b;
        }
        
        .urgent-notice {
          background: #fef3c7;
          border: 2px solid #f59e0b;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
          text-align: center;
        }
        
        .urgent-text {
          color: #92400e;
          font-weight: bold;
          font-size: 16px;
        }
        
        @media print {
          .container {
            padding: 0;
          }
          
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        ${includeHeader ? `
        <div class="header">
          <div class="company-info">
            <div class="company-name">${companyInfo.name}</div>
            <div class="company-details">
              ${companyInfo.address}<br>
              Phone: ${companyInfo.phone}<br>
              Email: ${companyInfo.email}
              ${companyInfo.website ? `<br>Website: ${companyInfo.website}` : ''}
            </div>
          </div>
          <div class="rfq-info">
            <div class="rfq-title">REQUEST FOR QUOTATION</div>
            <div class="rfq-number">${rfq.rfq_number}</div>
            <div class="rfq-date">Date: ${formatDate(rfq.created_at)}</div>
            <div class="rfq-date">
              Status: <span class="status-badge status-${rfq.status?.toLowerCase()}">${rfq.status}</span>
            </div>
          </div>
        </div>
        ` : ''}
        
        ${rfq.response_deadline && new Date(rfq.response_deadline) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) ? `
        <div class="urgent-notice">
          <div class="urgent-text">⚠️ URGENT: Response Required by ${formatDateTime(rfq.response_deadline)}</div>
        </div>
        ` : ''}
        
        <div class="details-section">
          <div class="detail-card">
            <div class="detail-title">RFQ Information</div>
            <div class="detail-row">
              <span class="detail-label">RFQ Number:</span>
              <span class="detail-value">${rfq.rfq_number}</span>
            </div>
            ${rfq.requisition_number ? `
            <div class="detail-row">
              <span class="detail-label">From Requisition:</span>
              <span class="detail-value">${rfq.requisition_number}</span>
            </div>
            ` : ''}
            <div class="detail-row">
              <span class="detail-label">Response Deadline:</span>
              <span class="detail-value">${formatDateTime(rfq.response_deadline)}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Created By:</span>
              <span class="detail-value">${rfq.created_by_name || 'N/A'}</span>
            </div>
          </div>
          
          <div class="detail-card">
            <div class="detail-title">Delivery Information</div>
            <div class="detail-row">
              <span class="detail-label">Location:</span>
              <span class="detail-value">${rfq.delivery_location_name || 'N/A'}</span>
            </div>
            ${rfq.delivery_address ? `
            <div class="detail-row">
              <span class="detail-label">Address:</span>
              <span class="detail-value">${rfq.delivery_address}</span>
            </div>
            ` : ''}
            <div class="detail-row">
              <span class="detail-label">Required Date:</span>
              <span class="detail-value">${formatDate(rfq.required_date)}</span>
            </div>
          </div>
        </div>
        
        ${rfq.supplier_names && rfq.supplier_names.length > 0 ? `
        <div class="suppliers-section">
          <div class="detail-title">Selected Suppliers</div>
          <div class="suppliers-grid">
            ${rfq.supplier_names.map(supplier => `
              <div class="supplier-card">
                <strong>${supplier}</strong>
              </div>
            `).join('')}
          </div>
        </div>
        ` : ''}
        
        <table class="items-table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Quantity</th>
              <th>Unit</th>
              <th>Specifications</th>
              <th>Est. Unit Cost</th>
            </tr>
          </thead>
          <tbody>
            ${rfq.items?.map(item => `
              <tr>
                <td>
                  <div class="font-medium">${item.product_name || 'N/A'}</div>
                  ${item.product_code ? `<div style="font-size: 12px; color: #6b7280;">${item.product_code}</div>` : ''}
                </td>
                <td>${item.quantity || 0}</td>
                <td>${item.unit_of_measure_name || 'N/A'}</td>
                <td>${item.specifications || '-'}</td>
                <td>${item.estimated_unit_cost ? `$${item.estimated_unit_cost.toLocaleString()}` : '-'}</td>
              </tr>
            `).join('') || '<tr><td colspan="5" style="text-align: center;">No items</td></tr>'}
          </tbody>
        </table>
        
        ${(rfq.notes || rfq.terms_and_conditions) ? `
        <div class="notes-section">
          ${rfq.notes ? `
          <div class="notes-card">
            <div class="notes-title">Notes</div>
            <div class="notes-content">${rfq.notes}</div>
          </div>
          ` : ''}
          
          ${rfq.terms_and_conditions ? `
          <div class="notes-card">
            <div class="notes-title">Terms and Conditions</div>
            <div class="notes-content">${rfq.terms_and_conditions}</div>
          </div>
          ` : ''}
        </div>
        ` : ''}
        
        ${includeFooter ? `
        <div class="footer">
          <p><strong>Instructions for Suppliers:</strong></p>
          <p>Please provide your quotation by the deadline specified above. Include unit prices, delivery times, and any relevant terms.</p>
          <p>For questions, contact: ${companyInfo.email} | ${companyInfo.phone}</p>
          <br>
          <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        ` : ''}
      </div>
    </body>
    </html>
  `;
};

export const downloadRFQPDF = async (
  rfq: RFQ,
  options?: RFQPDFOptions
) => {
  const htmlContent = generateRFQHTML(rfq, options);
  
  // Create a blob with the HTML content
  const blob = new Blob([htmlContent], { type: 'text/html' });
  const url = URL.createObjectURL(blob);
  
  // Create a temporary link and trigger download
  const link = document.createElement('a');
  link.href = url;
  link.download = `rfq-${rfq.rfq_number || 'draft'}.html`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  URL.revokeObjectURL(url);
};

export const printRFQ = (
  rfq: RFQ,
  options?: RFQPDFOptions
) => {
  const htmlContent = generateRFQHTML(rfq, options);
  
  // Open a new window for printing
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }
};
