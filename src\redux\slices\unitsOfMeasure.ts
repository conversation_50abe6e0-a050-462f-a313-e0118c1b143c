import { apiSlice } from "../apiSlice";

// Types for Unit of Measure API
export interface UnitOfMeasure {
  id?: number;
  name: string;
  symbol: string;
  description?: string;
  conversion_base?: string;
}

export const unitOfMeasureApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all units of measure
    getUnitsOfMeasure: builder.query<UnitOfMeasure[], any>({
      query: (params) => ({
        url: "/setup/units-of-measure",
        method: "GET",
        params: params,
      }),
      providesTags: ["UnitsOfMeasure"],
    }),

    // Get single unit of measure
    getUnitOfMeasure: builder.query<UnitOfMeasure, string>({
      query: (id) => ({
        url: `/setup/units-of-measure/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "UnitsOfMeasure", id }],
    }),

    // Create unit of measure
    createUnitOfMeasure: builder.mutation<UnitOfMeasure, Partial<UnitOfMeasure>>({
      query: (payload) => ({
        url: "/setup/units-of-measure",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["UnitsOfMeasure"],
    }),

    // Update unit of measure
    updateUnitOfMeasure: builder.mutation<UnitOfMeasure, { id: string; data: Partial<UnitOfMeasure> }>({
      query: ({ id, data }) => ({
        url: `/setup/units-of-measure/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "UnitsOfMeasure", id }, "UnitsOfMeasure"],
    }),

    // Delete unit of measure
    deleteUnitOfMeasure: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/units-of-measure/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["UnitsOfMeasure"],
    }),
  }),
});

export const { 
  useGetUnitsOfMeasureQuery, 
  useGetUnitOfMeasureQuery,
  useCreateUnitOfMeasureMutation,
  useUpdateUnitOfMeasureMutation,
  useDeleteUnitOfMeasureMutation,
} = unitOfMeasureApiSlice;
