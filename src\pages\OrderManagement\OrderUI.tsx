import { SetStateAction, useState, useEffect } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Card6 } from "@/components/custom/cards/Card6";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/custom/badges/badges";
import {
  Activity,
  House,
  TrendingUp,
  Users,
  BarChart3,
  Building2,
} from "lucide-react";
import { motion } from "framer-motion";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { CheckInModal } from "./NewOrder";
import OrderCard from "./Components/OrderCard";
import { useLazyGetOrdersQuery } from "@/redux/slices/order";
 // Adjust import path as needed
 // Import OrderCard

// TypeScript interfaces for API response
interface CurrentPeriod {
  period_name: string;
  start_date: string;
  end_date: string;
  target: number;
  achieved: number;
  progress: number;
}

interface OfficeData {
  office: string;
  total_marketers: number;
  current_period: CurrentPeriod;
}

// Define the ExtendedCheckInForm interface to match NewOrder.tsx
interface ExtendedCheckInForm {
  orderNumber: string;
  order_type: "DINE_IN" | "TAKEAWAY" | "DELIVERY" | "ROOM_SERVICE" | "DRIVE_THRU" | "ONLINE";
  status: "OPEN" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED" | "PENDING";
  total_amount: string;
  payment_status: boolean;
  guest_count: number;
  tax_amount: string;
  service_charge: string;
  catering_levy: string;
  revenue_center: number;
  workstation: number;
  table_number: number | null;
  created_by: string | null;
  specialRequests: string;
}

// Define Order type for OrderCard (API structure)
interface ApiOrder {
  id: number;
  order_number: string;
  order_type: string;
  status: string;
  order_date: string;
  total_amount: number;
  payment_status: string;
  guest_count?: number;
  created_at: string;
  modified_at: string;
  tax_amount?: number;
  service_charge?: number;
  catering_levy?: number;
  revenue_center?: string;
  workstation?: string;
  table_number?: string;
  created_by: string;
}

// Define Order type for OrderCard (normalized structure)
interface Order {
  orderNumber: string;
  status: string;
  date: string;
  total: string;
  id?: number;
  order_type?: string;
  payment_status?: string;
  guest_count?: number;
  table_number?: string;
}

function OrderDashboard() {
  const [newOrder, setNewOrder] = useState(false);
  const [checkInForm, setCheckInForm] = useState<ExtendedCheckInForm>({
    orderNumber: "",
    order_type: "DINE_IN",
    status: "OPEN",
    total_amount: "0.00",
    payment_status: false,
    guest_count: 1,
    tax_amount: "0.00",
    service_charge: "0.00",
    catering_levy: "0.00",
    revenue_center: 1,
    workstation: 1,
    table_number: null,
    created_by: null,
    specialRequests: "",
  });
  

  // Dummy order data (replace with API data)
  const [orders, setOrders] = useState<Order[]>([
    {
      orderNumber: "001",
      status: "Confirmed",
      date: "March 15, 2024",
      total: "$299.99",
    },
    {
      orderNumber: "002",
      status: "Pending",
      date: "March 16, 2024",
      total: "$149.50",
    },
    {
      orderNumber: "003",
      status: "Delivered",
      date: "March 17, 2024",
      total: "$89.99",
    },
  ]); 
  const [getOrders, { data, isLoading }] = useLazyGetOrdersQuery();

  // Call the trigger function to execute the query
  const handleGetOrders = () => {
    getOrders({});
  };

  console.log(data, "order")

  // Automatically fetch orders when component mounts
  useEffect(() => {
    getOrders({});
  }, [getOrders]);

  // Extract orders from API response
  const apiOrdersRaw: ApiOrder[] = data?.data?.results || [];
  console.log("API Orders Raw:", apiOrdersRaw);

  // Map API orders to the Order interface structure
  const apiOrders: Order[] = apiOrdersRaw.map((apiOrder) => ({
    orderNumber: apiOrder.order_number,
    status: apiOrder.status,
    date: new Date(apiOrder.order_date).toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    }),
    total: `$${apiOrder.total_amount.toFixed(2)}`,
    id: apiOrder.id,
    order_type: apiOrder.order_type,
    payment_status: apiOrder.payment_status,
    guest_count: apiOrder.guest_count,
    table_number: apiOrder.table_number,
  }));

  console.log("Mapped API Orders:", apiOrders);

  // Combine API orders with dummy orders (or replace dummy orders entirely)
  const allOrders = [...orders, ...apiOrders];

  // Handle form submission
  const handleCheckIn = () => {
    // Process the form data (e.g., send to API)
    console.log("Check-in submitted:", checkInForm);
    // Optionally add new order to the list
    setOrders((prev) => [
      ...prev,
      {
        orderNumber: checkInForm.orderNumber,
        status: checkInForm.status,
        date: new Date().toLocaleDateString("en-US", {
          month: "long",
          day: "numeric",
          year: "numeric",
        }),
        total: `$${checkInForm.total_amount}`,
        order_type: checkInForm.order_type,
        payment_status: checkInForm.payment_status ? "PAID" : "PENDING",
        guest_count: checkInForm.guest_count,
        table_number: checkInForm.table_number?.toString(),
      },
    ]);
    // Reset form and close modal
    setCheckInForm({
      orderNumber: "",
      order_type: "DINE_IN",
      status: "OPEN",
      total_amount: "0.00",
      payment_status: false,
      guest_count: 1,
      tax_amount: "0.00",
      service_charge: "0.00",
      catering_levy: "0.00",
      revenue_center: 1,
      workstation: 1,
      table_number: null,
      created_by: null,
      specialRequests: "",
    });
    setNewOrder(false);
  };

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 py-4 space-y-4">
          {/* Header with Office Cards */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4"
          >
            <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
                  <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                    Order Dashboard
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 text-base sm:text-lg">
                    Restaurant Order Management
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row flex-wrap gap-4 w-full lg:w-auto">
                <div className="flex-1 min-w-[180px] bg-blue-50 dark:bg-blue-900/20 rounded-xl px-4 py-3 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-3">
                    <Building2 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    <div>
                      <p className="text-blue-600 dark:text-blue-400 text-xs sm:text-sm font-medium">Total Tables</p>
                      <p className="text-blue-900 dark:text-blue-100 text-lg sm:text-xl font-bold">20</p>
                    </div>
                  </div>
                </div>
                <div className="flex-1 min-w-[180px] bg-green-50 dark:bg-green-900/20 rounded-xl px-4 py-3 border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-3">
                    <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <div>
                      <p className="text-green-600 dark:text-green-400 text-xs sm:text-sm font-medium">Total Orders</p>
                      <p className="text-green-900 dark:text-green-100 text-lg sm:text-xl font-bold">{allOrders.length}</p>
                    </div>
                  </div>
                </div>
                <div className="flex-1 min-w-[180px] bg-purple-50 dark:bg-purple-900/20 rounded-xl px-4 py-3 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center gap-3">
                    <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    <div>
                      <p className="text-purple-600 dark:text-purple-400 text-xs sm:text-sm font-medium">Avg Order Value</p>
                      <p className="text-purple-900 dark:text-purple-100 text-lg sm:text-xl font-bold">
                        $
                        {allOrders.length
                          ? (
                              allOrders.reduce((sum, order) => sum + parseFloat(order.total.replace("$", "")), 0) / allOrders.length
                            ).toFixed(2)
                          : "0.00"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Action Cards Section */}
          
            {/* New Order Card */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
                    <Activity className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">New Order</h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Create a new customer order</p>
                  </div>
                </div>
                <PrimaryButton
                  onClick={() => setNewOrder(true)}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  size="sm"
                >
                  Create Order
                </PrimaryButton>
              </div>
            </motion.div>

           
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6"
          >
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Recent Orders</h2>
            {allOrders.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {allOrders.map((order, index) => (
                  <OrderCard
                    key={order.id ? `api-${order.id}` : `dummy-${index}`}
                    orderNumber={order.orderNumber}
                    status={order.status}
                    date={order.date}
                    total={order.total}
                    payment_status={order.payment_status}
                    guest_count={order.guest_count}
                    table_number={order.table_number}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600 dark:text-gray-400">No orders available.</p>
              </div>
            )}
          </motion.div>

          {/* Modals */}
          <CheckInModal
            open={newOrder}
            onOpenChange={setNewOrder}
            checkInForm={checkInForm}
            setCheckInForm={setCheckInForm}
            
          />
          
          
        </div>
      </div>
    </Screen>
  );
}

export default OrderDashboard;