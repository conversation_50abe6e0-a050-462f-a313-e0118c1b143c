import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Loader2, AlertTriangle } from "lucide-react";
import {
  useCreatePurchaseRequisitionMutation,
  useGetStoreRequisitionsQuery,
  useGetProductsQuery,
  useGetUnitsOfMeasureQuery,
  useGetUsersQuery,
} from "@/redux/slices/procurement";
import { useAuthHook } from "@/utils/useAuthHook";
import { PurchaseRequisitionFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddPurchaseRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddPurchaseRequisition = ({ isOpen, onClose }: AddPurchaseRequisitionProps) => {
  const [createPurchaseRequisition, { isLoading: creating }] = useCreatePurchaseRequisitionMutation();
  const { user_details } = useAuthHook();

  // Fetch supporting data
  const { data: storeRequisitions } = useGetStoreRequisitionsQuery({});
  const { data: products } = useGetProductsQuery({});
  const { data: unitsOfMeasure } = useGetUnitsOfMeasureQuery({});
  const { data: users } = useGetUsersQuery({});

  const [formData, setFormData] = useState<PurchaseRequisitionFormData>({
    store_requisition: "", // required - links to Store Requisition
    created_by: "", // required - user creating the PR
    status: "Draft", // defaults to Draft
    items: [
      {
        product: "",
        quantity: "", // string as per API
        unit_of_measure: "",
      },
    ],
  });



  const handleInputChange = (field: keyof PurchaseRequisitionFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          estimated_unit_cost: "",
          specifications: "",
          preferred_supplier: "",
          justification: "",
        },
      ],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData((prev) => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index),
      }));
    }
  };

  // Auto-select current user if available
  useEffect(() => {
    if (user_details && users?.data?.results) {
      const currentUser = users.data.results.find((user: any) =>
        user.email === user_details.email || user.employee_no === user_details.employee_no
      );
      if (currentUser && !formData.created_by) {
        setFormData(prev => ({ ...prev, created_by: currentUser.id }));
      }
    }
  }, [user_details, users, formData.created_by]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation - Check all required fields per API specification
    if (!formData.store_requisition || !formData.created_by) {
      toast.error("Please fill in all required fields: Store Requisition and Created By");
      return;
    }

    if (formData.items.some(item => !item.product || !item.quantity || !item.unit_of_measure)) {
      toast.error("Please complete all item details: Product, Quantity, and Unit of Measure");
      return;
    }

    try {
      const payload = {
        store_requisition: Number(formData.store_requisition),
        created_by: Number(formData.created_by),
        status: formData.status || "Draft",
      };

      const result = await createPurchaseRequisition(payload).unwrap();

      // Create items separately if the PR was created successfully
      if (result.id && formData.items.length > 0) {
        // Note: Items would need to be created separately using createPurchaseRequisitionItem
        // For now, we'll just show success for the PR creation
      }

      toast.success("Purchase requisition created successfully");
      onClose();
      
      // Reset form
      setFormData({
        store_requisition: "",
        created_by: "",
        status: "Draft",
        items: [
          {
            product: "",
            quantity: "",
            unit_of_measure: "",
          },
        ],
      });
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create purchase requisition");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Purchase Requisition</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="store_requisition">Store Requisition *</Label>
                <Select
                  value={formData.store_requisition.toString()}
                  onValueChange={(value) => handleInputChange("store_requisition", Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select store requisition" />
                  </SelectTrigger>
                  <SelectContent>
                    {storeRequisitions?.data?.results?.map((sr: any) => (
                      <SelectItem key={sr.id} value={sr.id.toString()}>
                        SR-{String(sr.id).padStart(4, '0')} - {sr.purpose}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500">Select the store requisition to convert</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="created_by">Created By *</Label>
                <Select
                  value={formData.created_by.toString()}
                  onValueChange={(value) => handleInputChange("created_by", Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select user" />
                  </SelectTrigger>
                  <SelectContent>
                    {users?.data?.results?.map((user: any) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.fullnames || user.email} ({user.employee_no || 'No Emp#'})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500">User creating this purchase requisition</p>
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Items</CardTitle>
              <Button type="button" onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Product *</Label>
                      <Select
                        value={item.product.toString()}
                        onValueChange={(value) => handleItemChange(index, "product", Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {products?.data?.results?.map((product: any) => (
                            <SelectItem key={product.id} value={product.id.toString()}>
                              {product.name} ({product.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Quantity *</Label>
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                        placeholder="Enter quantity"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Unit of Measure *</Label>
                      <Select
                        value={item.unit_of_measure.toString()}
                        onValueChange={(value) => handleItemChange(index, "unit_of_measure", Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {unitsOfMeasure?.data?.results?.map((unit: any) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              {unit.name} ({unit.abbreviation})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Note: New API only requires product, quantity, unit_of_measure */}
                  <div className="text-xs text-gray-500 mt-2">
                    All fields above are required by the API. Items will be created separately after PR creation.
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Purchase Requisition
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPurchaseRequisition;
