import { apiSlice } from "../apiSlice";

// Types for User Role API
export interface UserRole {
  id?: number;
  name: string;
  is_active?: boolean;
}

export const userRoleApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all user roles
    getUserRoles: builder.query<UserRole[], any>({
      query: (params) => ({
        url: "/users/user_roles",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        // Handle different response structures
        if (Array.isArray(response)) {
          return response;
        } else if (response && response.data) {
          if (Array.isArray(response.data)) {
            return response.data;
          } else if (response.data.results && Array.isArray(response.data.results)) {
            return response.data.results;
          } else {
            return [response.data];
          }
        } else if (response && response.results && Array.isArray(response.results)) {
          return response.results;
        } else {
          return [];
        }
      },
      providesTags: ["UserRoles"],
    }),

    // Get single user role
    getUserRole: builder.query<UserRole, string>({
      query: (id) => ({
        url: `/users/user_roles/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "UserRoles", id }],
    }),

    // Create user role
    createUserRole: builder.mutation<UserRole, Partial<UserRole>>({
      query: (payload) => ({
        url: "/users/user_roles",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["UserRoles"],
    }),

    // Update user role
    updateUserRole: builder.mutation<UserRole, { id: string; data: Partial<UserRole> }>({
      query: ({ id, data }) => ({
        url: `/users/user_roles/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "UserRoles", id }, "UserRoles"],
    }),

    // Delete user role
    deleteUserRole: builder.mutation<void, string>({
      query: (id) => ({
        url: `/users/user_roles/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["UserRoles"],
    }),
  }),
});

export const { 
  useGetUserRolesQuery, 
  useGetUserRoleQuery,
  useCreateUserRoleMutation,
  useUpdateUserRoleMutation,
  useDeleteUserRoleMutation,
} = userRoleApiSlice;
