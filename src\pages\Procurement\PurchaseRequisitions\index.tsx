import { Screen } from "@/app-components/layout/screen";
import { useState } from "react";
import {
  useGetPurchaseRequisitionsQuery,
  useSubmitPurchaseRequisitionMutation,
  useApprovePurchaseRequisitionMutation,
  useRejectPurchaseRequisitionMutation,
  useDeletePurchaseRequisitionMutation,
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { PurchaseRequisition } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, Send, CheckCircle, XCircle, Plus, Trash2 } from "lucide-react";
import AddPurchaseRequisition from "./modals/AddPurchaseRequisition";
import RejectPurchaseRequisition from "./modals/RejectPurchaseRequisition";
import { toast } from "@/components/custom/Toast/MyToast";

const PurchaseRequisitionsIndex = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [selectedRequisition, setSelectedRequisition] = useState<any>(null);
  const [searchInput, setSearchInput] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [statusFilter, setStatusFilter] = useState("");

  // Fetch data
  const {
    data: purchaseRequisitions,
    isLoading,
    error,
    isFetching,
  } = useGetPurchaseRequisitionsQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
    status: statusFilter,
  });

  // Mutation hooks
  const [submitPurchaseRequisition, { isLoading: isSubmitting }] = useSubmitPurchaseRequisitionMutation();
  const [approvePurchaseRequisition, { isLoading: isApproving }] = useApprovePurchaseRequisitionMutation();
  const [rejectPurchaseRequisition, { isLoading: isRejecting }] = useRejectPurchaseRequisitionMutation();
  const [deletePurchaseRequisition, { isLoading: isDeleting }] = useDeletePurchaseRequisitionMutation();

  // Handler functions
  const handleSubmit = async (id: number) => {
    try {
      await submitPurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await approvePurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = (requisition: any) => {
    setSelectedRequisition(requisition);
    setIsRejectModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to delete this purchase requisition?")) return;

    try {
      await deletePurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition deleted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete requisition");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      Submitted: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Rejected: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const columns: ColumnDef<PurchaseRequisition>[] = [
    {
      accessorKey: "id",
      header: "PR Number",
      cell: (info) => (
        <Link
          to={`/procurement/purchase-requisitions/${info?.row?.original?.id}`}
          title="View Purchase Requisition"
        >
          <span className="font-medium underline capitalize text-blue-400">
            PR-{String(info.getValue() as number).padStart(4, '0')}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "store_requisition",
      header: "Store Requisition",
      cell: (info) => {
        const value = info.getValue() as number;
        return value ? (
          <Link
            to={`/procurement/store-requisitions/${value}`}
            className="text-blue-600 hover:underline"
          >
            SR-{String(value).padStart(4, '0')}
          </Link>
        ) : "N/A";
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_by_name",
      header: "Created By",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => getStatusBadge(info.getValue() as string),
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: (info) => {
        const date = info.getValue() as string;
        return date ? new Date(date).toLocaleDateString() : "N/A";
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const requisition = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/purchase-requisitions/${requisition.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              {requisition.status === "Draft" && (
                <>
                  <DropdownMenuItem>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSubmit(requisition.id!)}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(requisition.id!)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Submitted" && (
                <>
                  <DropdownMenuItem onClick={() => handleApprove(requisition.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleReject(requisition)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Rejected" && (
                <>
                  <DropdownMenuItem>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit & Resubmit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(requisition.id!)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Purchase Requisitions</h1>
            <p className="text-gray-600 mt-1">Manage purchase requests and approvals</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Purchase Requisition
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 items-center">
          {/* Status Filter */}
          <div className="flex gap-2">
            <Button
              variant={statusFilter === "" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("")}
            >
              All Status
            </Button>
            <Button
              variant={statusFilter === "Draft" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Draft")}
            >
              Draft
            </Button>
            <Button
              variant={statusFilter === "Submitted" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Submitted")}
            >
              Submitted
            </Button>
            <Button
              variant={statusFilter === "Approved" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Approved")}
            >
              Approved
            </Button>
          </div>
        </div>

        {/* Loading Indicator */}
        {(isFetching || isSubmitting || isApproving || isRejecting || isDeleting) && (
          <div className="flex items-center justify-center py-4 bg-blue-50 rounded-lg mb-4">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            <span className="text-blue-800 text-sm">
              {isFetching && "Loading data..."}
              {isSubmitting && "Submitting requisition..."}
              {isApproving && "Approving requisition..."}
              {isRejecting && "Rejecting requisition..."}
              {isDeleting && "Deleting requisition..."}
            </span>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading purchase requisitions...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <XCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-800">
                Failed to load purchase requisitions. Please try again.
              </span>
            </div>
          </div>
        )}

        {/* Data Table */}
        <DataTable<PurchaseRequisition>
          data={purchaseRequisitions?.data?.results || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          searchInput={
            <input
              value={searchInput}
              name="searchInput"
              type="search"
              onChange={(e) =>
                searchDebouncer(e.target.value, setSearchInput, setSearchValue)
              }
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search purchase requisitions..."
            />
          }
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={purchaseRequisitions?.data?.total_data || 0}
        />

        {isAddModalOpen && (
          <AddPurchaseRequisition
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}

        {isRejectModalOpen && selectedRequisition && (
          <RejectPurchaseRequisition
            isOpen={isRejectModalOpen}
            onClose={() => {
              setIsRejectModalOpen(false);
              setSelectedRequisition(null);
            }}
            requisitionId={selectedRequisition.id}
            requisitionNumber={`PR-${String(selectedRequisition.id).padStart(4, '0')}`}
          />
        )}
      </div>
    </Screen>
  );
};

export default PurchaseRequisitionsIndex;
