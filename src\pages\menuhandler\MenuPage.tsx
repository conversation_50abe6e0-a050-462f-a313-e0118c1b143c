import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import React, { useState } from "react";
import { MealCard } from "./Components/MealCard";
import { CreateMenuItemModal } from "./Components/AddFoodModal";
import { useGetMenusQuery } from "@/redux/slices/menuMake";

function FoodMenu() {
  const { data, isLoading, isError } = useGetMenusQuery(undefined);

  // Map API menu groups to meal cards
  const mappedMeals = Array.isArray(data?.data?.results)
    ? data.data.results.map((menu: any) => ({
        title: menu.name || "Untitled",
        description: menu.description || "",
        imageUrl: menu.image_url || "https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=400&q=80",
        price: menu.price ? `$${menu.price}` : "",
        category: menu.category || "Uncategorized",
        rating: menu.rating || 0,
        prepTime: menu.prep_time || "",
      }))
    : [];

  const [meals, setMeals] = useState(mappedMeals);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Update meals when API data changes
  React.useEffect(() => {
    setMeals(mappedMeals);
  }, [data]);

  type Meal = {
    title: string;
    description: string;
    imageUrl: string;
    price: string;
    category: string;
    rating: number;
    prepTime: string;
  };

  const handleAddMenuItem = (newItem: Meal) => {
    setMeals((prev: Meal[]) => [...prev, newItem]);
  };

  return (
    <Screen>
      <header className="mb-6">
        <div className="flex-1">
          <div className="relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-red-600 rounded-2xl shadow-2xl animate-pulse">
            <div className="px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <div className="w-8 h-8 bg-white/30 rounded"></div>
                </div>
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold text-white drop-shadow">Food Menu</h1>
                  <div className="h-4 bg-white/20 rounded w-48"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div className="flex justify-end mb-6">
        <PrimaryButton variant="secondary" onClick={() => setIsModalOpen(true)}>
          Add Menu Item
        </PrimaryButton>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {meals.map((meal: Meal) => (
          <MealCard
            key={meal.title}
            title={meal.title}
            description={meal.description}
            imageUrl={meal.imageUrl}
            price={meal.price}
            category={meal.category}
            rating={meal.rating}
            prepTime={meal.prepTime}
            onClick={() => {
              /* open modal logic here if needed */
            }}
            className="max-w-xs"
          />
        ))}
      </div>
      {isModalOpen && (
        <CreateMenuItemModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddMenuItem}
        />
      )}
    </Screen>
  );
}

export default FoodMenu;