import { apiSlice } from "../apiSlice";

export const taxApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getTaxRates: builder.query({
      query: (params) => ({
        url: "/tax-rates",
        method: "GET",
        params: params,
      }),
    }),

    retrieveTaxRate: builder.query({
      query: (id) => ({
        url: `/tax-rates/${id}`,
        method: "GET",
      }),
    }),

    addTaxRates: builder.mutation({
      query: (payload) => ({
        url: "/tax-rates",
        method: "POST",
        body: payload,
      }),
    }),

    patchTaxRates: builder.mutation({
      query: (payload) => ({
        url: `/tax-rates/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),

    getTaxClasses: builder.query({
      query: (params) => ({
        url: "/tax-classes",
        method: "GET",
        params: params,
      }),
    }),

    retrieveTaxClass: builder.query({
      query: (id) => ({
        url: `/tax-classes/${id}`,
        method: "GET",
      }),
    }),

    addTaxClass: builder.mutation({
      query: (payload) => ({
        url: "/tax-classes",
        method: "POST",
        body: payload,
      }),
    }),

    patchTaxClasses: builder.mutation({
      query: (payload) => ({
        url: `/tax-classes/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetTaxRatesQuery,
  useRetrieveTaxRateQuery,
  useAddTaxRatesMutation,
  usePatchTaxRatesMutation,

  useGetTaxClassesQuery,
  useRetrieveTaxClassQuery,
  useAddTaxClassMutation,
  usePatchTaxClassesMutation,

  useLazyGetTaxRatesQuery,
  useLazyRetrieveTaxRateQuery,
  useLazyGetTaxClassesQuery,
  useLazyRetrieveTaxClassQuery,
} = taxApiSlice;
