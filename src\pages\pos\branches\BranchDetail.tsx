import React, { useState } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Screen } from '@/app-components/layout/screen';
import { 
  ArrowLeft, 
  Edit, 
  Building2, 
  MapPin, 
  Clock, 
  DollarSign,
  Store,
  Monitor,
  Calculator,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Branch, RevenueCenter, Workstation } from '@/types/pos';

import { useGetBranchQuery } from '@/redux/slices/branches';

const BranchDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  // API hook to fetch branch data
  const { data: branch, isLoading, error } = useGetBranchQuery(id || '');

  // Handle loading state
  if (isLoading) {
    return (
      <Screen>
        <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading branch details...</p>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  // Handle error state
  if (error || !branch) {
    return (
      <Screen>
        <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <p className="text-muted-foreground mb-4">
                {error ? 'Error loading branch details' : 'Branch not found'}
              </p>
              <Button onClick={() => navigate('/pos/branches')}>
                Back to Branches
              </Button>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/pos/branches')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <Building2 className="h-6 w-6" />
              <h1 className="text-3xl font-bold tracking-tight">{branch.name}</h1>
              <Badge variant={branch.is_active ? 'default' : 'secondary'}>
                {branch.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Branch Code: {branch.code} • {branch.location}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link to={`/pos/branches/${branch.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Branch
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                {branch.is_active ? 'Deactivate' : 'Activate'} Branch
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Export Configuration</DropdownMenuItem>
              <DropdownMenuItem>Duplicate Branch</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue-centers">Revenue Centers</TabsTrigger>
          <TabsTrigger value="workstations">Workstations</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{branch.location}</div>
                    <div className="text-sm text-muted-foreground">Physical Address</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Timezone</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{branch.timezone}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Currency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{branch.currency}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Tax Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Calculator className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{branch.tax_config ? 'Configured' : 'Not configured'}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Store className="h-5 w-5" />
                  <span>Revenue Centers</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{branch.revenueCenters?.length || 0}</div>
                <p className="text-sm text-muted-foreground">Active business units</p>
                <Link to={`/pos/revenue-centers?branch=${branch.id}`}>
                  <Button variant="outline" size="sm" className="mt-2">
                    View All
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Monitor className="h-5 w-5" />
                  <span>Workstations</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{branch.workstations?.length || 0}</div>
                <p className="text-sm text-muted-foreground">POS devices</p>
                <Link to={`/pos/workstations?branch=${branch.id}`}>
                  <Button variant="outline" size="sm" className="mt-2">
                    View All
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calculator className="h-5 w-5" />
                  <span>Tax Configuration</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {branch.tax_config && (
                    <Badge variant="outline">
                      {JSON.stringify(branch.tax_config)}
                    </Badge>
                  )}
                </div>
                <Link to={`/pos/tax-configuration`}>
                  <Button variant="outline" size="sm" className="mt-2">
                    Manage Tax
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue-centers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Centers</CardTitle>
              <CardDescription>
                Business units within this branch that generate income
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {branch.revenueCenters?.map((rc) => (
                  <div key={rc.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <div className="font-medium">{rc.name}</div>
                      <div className="text-sm text-muted-foreground">Revenue Center</div>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline">{rc.code}</Badge>
                        <Badge variant="default">Active</Badge>
                      </div>
                    </div>
                    <Link to={`/pos/revenue-centers/${rc.id}`}>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workstations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Workstations</CardTitle>
              <CardDescription>
                POS devices and terminals configured for this branch
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {branch.workstations?.map((ws) => (
                  <div key={ws.workstation_id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <div className="font-medium">{ws.name}</div>
                      <div className="text-sm text-muted-foreground">{ws.role}</div>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline">{ws.ip_address}</Badge>
                        <Badge variant={ws.is_online ? 'default' : 'secondary'}>
                          {ws.is_online ? 'Online' : 'Offline'}
                        </Badge>
                      </div>
                    </div>
                    <Link to={`/pos/workstations/${ws.workstation_id}`}>
                      <Button variant="outline" size="sm">
                        Configure
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Branch Settings</CardTitle>
              <CardDescription>
                Configuration and operational settings for this branch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Localization</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Timezone:</span>
                      <span className="font-medium">{branch.timezone}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Currency:</span>
                      <span className="font-medium">{branch.currency}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Status</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Active:</span>
                      <Badge variant={branch.is_active ? 'default' : 'secondary'}>
                        {branch.is_active ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Language:</span>
                      <span className="font-medium">{branch.language}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </Screen>
  );
};

export default BranchDetail;
