import React, { useState, useEffect } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { motion } from "framer-motion";

// Define Order type for props
interface Order {
  orderNumber: string;
  status: string;
  date: string;
  total: string;
  tableNumber?: string; // Optional, from CheckInModal
  guestCount?: string; // Optional, from CheckInModal
}

interface OrderTrackerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: Order;
}

function OrderTrackerPipeline({ open, onOpenChange, order }: OrderTrackerProps) {
  const [currentStep, setCurrentStep] = useState(2); // Demo: currently in preparation
  const [isBeeping, setIsBeeping] = useState(true);
  const [isCancelled, setIsCancelled] = useState(order.status === "Cancelled");

  // Beeping animation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setIsBeeping((prev) => !prev);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const orderSteps = [
    {
      id: 1,
      name: "Ordered",
      description: "Order placed successfully",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      ),
      time: "2 min ago",
    },
    {
      id: 2,
      name: "Preparation",
      description: "Chef is preparing your order",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          />
        </svg>
      ),
      time: "In progress",
    },
    {
      id: 3,
      name: "Serving",
      description: "Waiter is serving your order",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
      ),
      time: "Pending",
    },
    {
      id: 4,
      name: "Eating",
      description: "Enjoy your meal!",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
      ),
      time: "Pending",
    },
    {
      id: 5,
      name: "Paying",
      description: "Processing payment",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
          />
        </svg>
      ),
      time: "Pending",
    },
    {
      id: 6,
      name: "Paid",
      description: "Order completed successfully",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
      time: "Pending",
    },
  ];

  const getStepStatus = (stepId: number) => {
    if (isCancelled) return "cancelled";
    if (stepId < currentStep) return "completed";
    if (stepId === currentStep) return "current";
    return "pending";
  };

  const getStepClasses = (stepId: number) => {
    const status = getStepStatus(stepId);
    const baseClasses = "relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300";

    if (status === "cancelled") {
      return `${baseClasses} bg-destructive/10 border-destructive/30 text-destructive`;
    }
    if (status === "completed") {
      return `${baseClasses} bg-primary border-primary text-white`;
    }
    if (status === "current") {
      const beepClasses = isBeeping ? "ring-4 ring-secondary/20 scale-110" : "ring-2 ring-secondary/10";
      return `${baseClasses} bg-secondary border-secondary text-white ${beepClasses}`;
    }
    return `${baseClasses} bg-muted border-outline text-muted-foreground`;
  };

  const getConnectorClasses = (stepId: number) => {
    const status = getStepStatus(stepId);
    if (isCancelled) return "bg-destructive/30";
    if (status === "completed") return "bg-primary";
    return "bg-outline";
  };

  const simulateProgress = () => {
    if (currentStep < 6 && !isCancelled) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const toggleCancel = () => {
    setIsCancelled((prev) => !prev);
  };

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title={`Order #${order.orderNumber} Tracker`}
      description="Track your order in real-time"
      className="max-w-4xl"
      size="lg"
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex flex-col gap-6 p-4 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-md"
      >
        {/* Order Status Banner */}
        <div className="px-6 py-4 bg-white border-b">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-800">
                {isCancelled ? "Order Cancelled" : `Current Status: ${orderSteps[currentStep - 1]?.name}`}
              </h2>
              <p className="text-sm text-gray-600">
                {isCancelled ? "Your order has been cancelled" : orderSteps[currentStep - 1]?.description}
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={simulateProgress}
                disabled={currentStep >= 6 || isCancelled}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:bg-muted disabled:text-muted-foreground transition-colors text-sm"
              >
                Next Step
              </button>
              <button
                onClick={toggleCancel}
                className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                  isCancelled ? "bg-primary text-primary-foreground hover:bg-primary/90" : "bg-destructive text-destructive-foreground hover:bg-destructive/90"
                }`}
              >
                {isCancelled ? "Restore" : "Cancel"}
              </button>
            </div>
          </div>
        </div>

        {/* Progress Pipeline */}
        <div className="px-6 py-8 bg-white">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Progress Line */}
              <div className="absolute top-6 left-6 right-6 h-1 bg-muted rounded-full">
                <div
                  className={`h-full rounded-full transition-all duration-700 ${
                    isCancelled ? "bg-destructive" : "bg-gradient-to-r from-primary to-secondary"
                  }`}
                  style={{ width: `${((currentStep - 1) / 5) * 100}%` }}
                />
              </div>

              {/* Steps */}
              <div className="relative flex justify-between">
                {orderSteps.map((step, index) => (
                  <div key={step.id} className="flex flex-col items-center">
                    {/* Step Circle */}
                    <div className={getStepClasses(step.id)}>
                      {getStepStatus(step.id) === "completed" ? (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : isCancelled ? (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      ) : (
                        step.icon
                      )}
                    </div>

                    {/* Step Info */}
                    <div className="mt-4 text-center">
                      <h3
                        className={`text-sm font-semibold ${
                          getStepStatus(step.id) === "current"
                            ? "text-secondary"
                            : getStepStatus(step.id) === "completed"
                            ? "text-primary"
                            : isCancelled
                            ? "text-destructive"
                            : "text-muted-foreground"
                        }`}
                      >
                        {step.name}
                      </h3>
                      <p className="text-xs text-muted-foreground mt-1 max-w-20">{step.description}</p>
                      <p className="text-xs text-muted-foreground/70 mt-1">
                        {getStepStatus(step.id) === "current"
                          ? step.time
                          : getStepStatus(step.id) === "completed"
                          ? "✓"
                          : isCancelled
                          ? "✗"
                          : step.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Order Details Card */}
        <div className="px-6 py-4">
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">Order Details</h3>
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  isCancelled
                    ? "bg-destructive/10 text-destructive"
                    : currentStep === 6
                    ? "bg-primary/10 text-primary"
                    : "bg-secondary/10 text-secondary"
                }`}
              >
                {isCancelled ? "Cancelled" : currentStep === 6 ? "Completed" : "In Progress"}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Order Time</p>
                <p className="font-medium">{new Date(order.date).toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit" })}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Estimated Delivery</p>
                <p className="font-medium">
                  {new Date(new Date(order.date).getTime() + 45 * 60000).toLocaleTimeString("en-US", {
                    hour: "numeric",
                    minute: "2-digit",
                  })}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Table Number</p>
                <p className="font-medium">{order.tableNumber || "N/A"}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Guest Count</p>
                <p className="font-medium">{order.guestCount || "N/A"}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Total</p>
                <p className="font-medium">{order.total}</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </BaseModal>
  );
}

export default OrderTrackerPipeline;