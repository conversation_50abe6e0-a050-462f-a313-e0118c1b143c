import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2, Store, X } from 'lucide-react';
import { RevenueCenterFormData } from '@/types/pos';
import { useCreateRevenueCenterMutation } from '@/redux/slices/revenueCenters';
import { generateRevenueCenterCode } from '@/utils/pos';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';

interface AddRevenueCenterModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  branchId: string;
  onSuccess?: () => void;
}

const AddRevenueCenterModal: React.FC<AddRevenueCenterModalProps> = ({
  isOpen,
  onOpenChange,
  branchId,
  onSuccess,
}) => {
  const [createRevenueCenter, { isLoading }] = useCreateRevenueCenterMutation();

  const form = useForm<RevenueCenterFormData>({
    defaultValues: {
      revenue_center_code: '',
      name: '',
      branch: branchId,
      is_active: true,
    },
  });

  const watchName = form.watch('name');

  // Auto-generate code when name changes
  React.useEffect(() => {
    if (watchName && !form.getValues('revenue_center_code')) {
      const generatedCode = generateRevenueCenterCode(watchName);
      form.setValue('revenue_center_code', generatedCode);
    }
  }, [watchName, form]);

  const onSubmit = async (data: RevenueCenterFormData) => {
    try {
      const result = await createRevenueCenter(data).unwrap();
      handleApiSuccess('Revenue center created successfully');
      form.reset();
      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      handleApiError(error, 'Failed to create revenue center');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            Add Revenue Center
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              rules={{ required: 'Revenue center name is required' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Revenue Center Name *</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., Main Restaurant, Bar, Takeaway" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="revenue_center_code"
              rules={{ required: 'Revenue center code is required' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Revenue Center Code *</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Auto-generated from name" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Active Status</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Enable this revenue center for operations
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Create Revenue Center
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddRevenueCenterModal;
