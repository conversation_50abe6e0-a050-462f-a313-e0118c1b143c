import {
  ProductCategory,
  ProductMainCategory,
  ProductMainCategoryApiResponse,
} from "@/types/products";
import { apiSlice } from "../apiSlice";

export const ProductCategoryApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getProductMainCategory: builder.query({
      query: (params) => ({
        url: "/inventory/product-main-categories",
        method: "GET",
        params: params,
      }),
      providesTags: ["ProductMainCategories"],
    }),

    retrieveProductMainCategory: builder.query({
      query: (id) => ({
        url: `/inventory/product-main-categories/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [
        { type: "ProductMainCategories", id },
      ],
    }),

    addProductMainCategory: builder.mutation({
      query: (payload) => ({
        url: "/inventory/product-main-categories",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["ProductMainCategories"],
    }),

    patchProductMainCategory: builder.mutation({
      query: (payload) => ({
        url: `/inventory/product-main-categories/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "ProductMainCategories", id },
        "ProductMainCategories",
      ],
    }),

    getProductCategory: builder.query({
      query: (params) => ({
        url: "/inventory/product-sub-categories",
        method: "GET",
        params: params,
      }),
      providesTags: ["ProductCategories"],
    }),

    retrieveProductCategory: builder.query({
      query: (id) => ({
        url: `/inventory/product-sub-categories/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "ProductCategories", id }],
    }),

    addProductCategory: builder.mutation({
      query: (payload) => ({
        url: "/inventory/product-sub-categories",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["ProductCategories"],
    }),

    patchProductCategory: builder.mutation({
      query: (payload) => ({
        url: `/inventory/product-sub-categories/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "ProductCategories", id },
        "ProductCategories",
      ],
    }),
  }),
});

export const {
  useGetProductMainCategoryQuery,
  useRetrieveProductMainCategoryQuery,
  useAddProductMainCategoryMutation,
  usePatchProductMainCategoryMutation,

  useGetProductCategoryQuery,
  useRetrieveProductCategoryQuery,
  useAddProductCategoryMutation,
  usePatchProductCategoryMutation,

  useLazyGetProductMainCategoryQuery,
  useLazyRetrieveProductMainCategoryQuery,
  useLazyGetProductCategoryQuery,
  useLazyRetrieveProductCategoryQuery,
} = ProductCategoryApiSlice;
