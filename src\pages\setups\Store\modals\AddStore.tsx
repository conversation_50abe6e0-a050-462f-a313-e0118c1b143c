import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import CustomSelectField from "@/components/CustomSelectField";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useLazyGetBranchesQuery } from "@/redux/slices";
import {
  useAddStoresMutation,
  usePatchStoresMutation,
} from "@/redux/slices/store";
import { storeTypes } from "@/types/store";
import { genRandomString } from "@/utils/helpers";
import { Loader2, Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  updateData?: storeTypes;
  refetch: () => void;
}

const AddStore = ({ isOpen, onClose, updateData, refetch }: propTypes) => {
  const [createStore, { isLoading: loading }] = useAddStoresMutation();
  const [updateStore, { isLoading: loadingUpdate }] = usePatchStoresMutation();
  const [fetchBranches, { data: branches, isLoading: loadingBranches }] =
    useLazyGetBranchesQuery();

  const [formData, setFormData] = useState({
    name: updateData ? updateData.name : "",
    location: updateData ? updateData.location : "",
    branch: updateData ? updateData.branch : "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddStore = async (e: React.FormEvent) => {
    e.preventDefault();
    const code = genRandomString(6);
    try {
      if (updateData) {
        await updateStore({ id: updateData.id, ...formData }).unwrap();
        refetch();
        toast.success("Store updated successfully");
      } else {
        await createStore({ ...formData, code: `ST${code}` }).unwrap();
        toast.success("Store created successfully");
      }
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "An error occurred");
    }
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title={updateData ? "Update Store" : "Add Store"}
      description="Enter store details"
    >
      <form onSubmit={handleAddStore}>
        <div className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="name">Name*</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter store name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">Location*</Label>
            <Input
              id="location"
              name="location"
              value={formData.location}
              onChange={handleInputChange}
              placeholder="Enter store location"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="branch">
              Branch*{" "}
              {updateData && (
                <span className="text-sm text-muted-foreground">
                  (Current: {updateData?.branch})
                </span>
              )}
            </Label>
            <CustomSelectField
              setValue={(e: any) =>
                handleInputChange({
                  target: { name: "branch", value: e },
                } as any)
              }
              useSearchField
              valueField="branch_code"
              labelField="name"
              data={branches || []}
              queryFunc={fetchBranches}
              loader={loadingBranches}
              isMultiple={false}
            />
          </div>
        </div>

        <div className="flex justify-end gap-4 mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {loading || loadingUpdate ? (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </Button>
          ) : (
            <Button type="submit">
              {updateData ? "Update Store" : "Add Store"}
              <Send className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </form>
    </BaseModal>
  );
};

export default AddStore;
