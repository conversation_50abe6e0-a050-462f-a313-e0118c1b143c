import { apiSlice } from "../apiSlice";

export const costCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMenuGroups: builder.query({
      query: (params) => ({
        url: "/menu/menu-groups",
        method: "GET",
        params,
        
      }),
    }),

    retrieveMenuGroups: builder.query({
      query: (id) => ({
        url: `/menu/menu-groups${id}`,
        method: "GET",
      }),
    }),

    addMenuGroups: builder.mutation({
      query: (payload) => ({
        url: "/menu/menu-groups", // Removed ID from URL, as POST typically creates a new resource
        method: "POST",
        body: payload,
      }),
    }),

    deleteMenuGroups: builder.mutation({
      query: (id) => ({
        url: `/menu/menu-groups${id}`,
        method: "DELETE",
      }),
    }),

    patchMenus: builder.mutation({
      query: (payload) => ({
        url: `/menu/menu-groups${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
    useGetMenuGroupsQuery,
    useLazyGetMenuGroupsQuery,
    usePatchMenusMutation,
    useAddMenuGroupsMutation,
    useDeleteMenuGroupsMutation,
} = costCenterApiSlice;
