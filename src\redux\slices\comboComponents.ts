import { apiSlice } from "../apiSlice";

export const comboMenuApi = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        
        getComboMenus: builder.query({
            query: () => '/combo-menu',
            providesTags: ['ComboComponent']
        }),
        
        
        getComboMenu: builder.query({
            query: (id) => `/combo-menu/${id}`,
            providesTags: (result, error, id) => [{ type: 'ComboMenu', id }]
        }),
        
       
        createComboMenu: builder.mutation({
            query: (comboMenuData) => ({
                url: '/combo-menu',
                method: 'POST',
                body: comboMenuData
            }),
            invalidatesTags: ['ComboComponent']
        }),
        
        
        updateComboMenu: builder.mutation({
            query: ({ id, ...comboMenuData }) => ({
                url: `/combo-menu/${id}`,
                method: 'PUT',
                body: comboMenuData
            }),
            invalidatesTags: (result, error, { id }) => [{ type: 'ComboMenu', id }]
        }),
        
        patchComboMenu: builder.mutation({
            query: ({ id, ...partialData }) => ({
                url: `/combo-menu/${id}`,
                method: 'PATCH',
                body: partialData
            }),
            invalidatesTags: (result, error, { id }) => [{ type: 'ComboMenu', id }]
        }),
        
       
        deleteComboMenu: builder.mutation({
            query: (id) => ({
                url: `/combo-menu/${id}`,
                method: 'DELETE'
            }),
            invalidatesTags: ['ComboMenu']
        })
    })
})


export const {
    useGetComboMenusQuery,
    useGetComboMenuQuery,
    useCreateComboMenuMutation,
    useUpdateComboMenuMutation,
    usePatchComboMenuMutation,
    useDeleteComboMenuMutation
} = comboMenuApi 