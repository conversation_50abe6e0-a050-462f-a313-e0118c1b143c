import { apiSlice } from "../apiSlice";

// Types for Tax Class API based on API specification
export interface TaxClass {
  id?: number;
  name: string;
  description?: string;
  Branch?: string;
  rates: number[];
}

export const taxClassApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all tax classes
    getTaxClasses: builder.query<TaxClass[], any>({
      query: (params) => ({
        url: "/setup/tax-classes",
        method: "GET",
        params: params,
      }),
      providesTags: ["TaxClasses"],
    }),

    // Get single tax class
    getTaxClass: builder.query<TaxClass, string>({
      query: (id) => ({
        url: `/setup/tax-classes/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "TaxClasses", id }],
    }),

    // Create tax class
    createTaxClass: builder.mutation<TaxClass, Partial<TaxClass>>({
      query: (payload) => ({
        url: "/setup/tax-classes",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["TaxClasses"],
    }),

    // Update tax class
    updateTaxClass: builder.mutation<TaxClass, { id: string; data: Partial<TaxClass> }>({
      query: ({ id, data }) => ({
        url: `/setup/tax-classes/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "TaxClasses", id }, "TaxClasses"],
    }),

    // Delete tax class
    deleteTaxClass: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/tax-classes/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["TaxClasses"],
    }),
  }),
});

export const { 
  useGetTaxClassesQuery, 
  useGetTaxClassQuery,
  useCreateTaxClassMutation,
  useUpdateTaxClassMutation,
  useDeleteTaxClassMutation,
} = taxClassApiSlice;
