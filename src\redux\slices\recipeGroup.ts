import { apiSlice } from "../apiSlice";

export const recipeGroupsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getRecipeGroup: builder.query({
      query: (params) => ({
        url: "/menu/recipe-groups",
        method: "GET",
        params,
        
      }),
    }),

    retrieveRecipesGroups: builder.query({
      query: (id) => ({
        url: `/menu/recipe-groups${id}`,
        method: "GET",
      }),
    }),

    addrecipesGroups: builder.mutation({
      query: (payload) => ({
        url: "/menu/recipe-groups", // Removed ID from URL, as POST typically creates a new resource
        method: "POST",
        body: payload,
      }),
    }),

    deleterecipesGroups: builder.mutation({
      query: (id) => ({
        url: `/menu/recipe-groups${id}`,
        method: "DELETE",
      }),
    }),

    patchrecipesGroups: builder.mutation({
      query: (payload) => ({
        url: `/menu/recipe-groups/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
    useGetRecipeGroupQuery,
    useLazyGetRecipeGroupQuery,
    usePatchrecipesGroupsMutation,
    useAddrecipesGroupsMutation,
    useDeleterecipesGroupsMutation,
    
    
} = recipeGroupsApiSlice;
