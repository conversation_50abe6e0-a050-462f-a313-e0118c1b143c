import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Plus, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Screen } from "@/app-components/layout/screen";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { searchDebouncer } from "@/utils/debouncers";
import { supplierCategoryTypes } from "@/types/suppliers";
import AddSupplierCategory from "./modals/AddSupplierCategory";
import { useGetSupplierCategoriesQuery } from "@/redux/slices/procurement";
import StoreDetails from "../Store/modals/StoreDetails";
import SupplierCategoryDetails from "./modals/SupplierCategoryDetails";

const SupplierCategories = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] =
    useState<supplierCategoryTypes | null>(null);

  const { data: categories, isLoading: catsLoading } =
    useGetSupplierCategoriesQuery({
      page: currentPage,
      page_size: itemsPerPage,
      search: searchValue,
    });

  const handleViewDetails = (category: supplierCategoryTypes) => {
    setSelectedCategory(category);
    setIsDetailModalOpen(true);
  };

  const handleEdit = (category: supplierCategoryTypes) => {
    setSelectedCategory(category);
    setIsEditModalOpen(true);
  };

  const columns: ColumnDef<supplierCategoryTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => <span className="">{row.original.name}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "description",
      header: "Description",
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewDetails(row.original)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row.original)}
          >
            Edit
          </Button>
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Supplier Categories
            </h1>
            <p className="text-muted-foreground">
              Manage supplier categories for your business
            </p>
          </div>
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        </div>

        {/* Data Table */}
        <DataTable<supplierCategoryTypes>
          data={categories?.data?.results || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          searchInput={
            <input
              value={searchInput}
              name="searchInput"
              type="search"
              onChange={(e) =>
                searchDebouncer(e.target.value, setSearchInput, setSearchValue)
              }
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search stores..."
            />
          }
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={categories?.data?.total_data || 0}
        />

        {/* Modal Components */}
        {isAddModalOpen && (
          <AddSupplierCategory
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}

        {isEditModalOpen && (
          <AddSupplierCategory
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            updateData={selectedCategory!}
          />
        )}

        {isDetailModalOpen && (
          <SupplierCategoryDetails
            isOpen={isDetailModalOpen}
            onClose={() => setIsDetailModalOpen(false)}
            supplierCategory={selectedCategory!}
          />
        )}
      </div>
    </Screen>
  );
};

export default SupplierCategories;
