import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import CustomSelectField from "@/components/CustomSelectField";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useLazyGetBranchesQuery } from "@/redux/slices";
import {
  useAddSuppliersMutation,
  useLazyGetSupplierCategoriesQuery,
  usePatchSuppliersMutation,
} from "@/redux/slices/suppliers";
import { supplierType } from "@/types/suppliers";
import { Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  updateData?: supplierType;
}

const AddSupplier = ({ isOpen, onClose, updateData }: propTypes) => {
  const [createSupplier, { isLoading: loading }] = useAddSuppliersMutation();
  const [updateSupplier, { isLoading: loadingUpdate }] =
    usePatchSuppliersMutation();
  const [fetchBranches, { data: branches, isLoading: loadingBranches }] =
    useLazyGetBranchesQuery();
  const [
    fetchSupplierCategories,
    { data: supplierCategories, isLoading: loadingSupplierCategories },
  ] = useLazyGetSupplierCategoriesQuery();
  const [openTab, setOpenTab] = useState(1);

  const [formData, setformData] = useState<supplierType>({
    name: updateData?.name || "",
    category: updateData?.category || "",
    branch: updateData?.branch || "",
    code: updateData?.code || "",
    contact_name: updateData?.contact_name || "",
    email: updateData?.email || "",
    phone_number: updateData?.phone_number || "",
    alt_phone_number: updateData?.alt_phone_number || "",
    address_physical: updateData?.address_physical || "",
    address_mailing: updateData?.address_mailing || "",
    country: updateData?.country || "",
    city_or_state: updateData?.city_or_state || "",
    tax_vat_16: updateData?.tax_vat_16 || false,
    tax_exempt: updateData?.tax_exempt || false,
    payment_terms: updateData?.payment_terms || "",
    status: updateData?.status || "",
    bank_details: {
      bank_name: updateData?.bank_details?.bank_name || "",
      account_name: updateData?.bank_details?.account_name || "",
      account_number: updateData?.bank_details?.account_number || "",
      branch_name: updateData?.bank_details?.branch_name || "",
      swift_code: updateData?.bank_details?.swift_code || "",
      swift_iban: updateData?.bank_details?.swift_iban || "",
      bank_code: updateData?.bank_details?.bank_code || "",
    },
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setformData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setformData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleAddSupplier = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      let res: any;
      if (updateData) {
        res = await updateSupplier({ id: updateData.id, ...formData }).unwrap();
        toast.success("Supplier updated successfully");
      } else {
        res = await createSupplier(formData).unwrap();
        toast.success("Supplier added successfully");
      }
      if (res?.id) {
        setformData({
          name: updateData?.name || "",
          category: updateData?.category || "",
          branch: updateData?.branch || "",
          contact_name: updateData?.contact_name || "",
          email: updateData?.email || "",
          phone_number: updateData?.phone_number || "",
          alt_phone_number: updateData?.alt_phone_number || "",
          address_physical: updateData?.address_physical || "",
          address_mailing: updateData?.address_mailing || "",
          country: updateData?.country || "",
          city_or_state: updateData?.city_or_state || "",
          tax_vat_16: updateData?.tax_vat_16 || false,
          tax_exempt: updateData?.tax_exempt || false,
          payment_terms: updateData?.payment_terms || "",
          status: updateData?.status || "",
          bank_details: {
            bank_name: updateData?.bank_details?.bank_name || "",
            account_name: updateData?.bank_details?.account_name || "",
            account_number: updateData?.bank_details?.account_number || "",
            branch_name: updateData?.bank_details?.branch_name || "",
            swift_code: updateData?.bank_details?.swift_code || "",
            swift_iban: updateData?.bank_details?.swift_iban || "",
            bank_code: updateData?.bank_details?.bank_code || "",
          },
        });
        onClose();
        toast.success("Supplier added successfully");
      }
    } catch (error: any) {
      let errorMsg;
      if (error?.data) {
        errorMsg = error.data?.message;
      } else {
        errorMsg = "Failed to add supplier. Please try again.";
      }
      toast.error(
        typeof errorMsg === "string" ? errorMsg : JSON.stringify(errorMsg)
      );
      console.error("Failed to add supplier:", error);
    }
  };

  const validateAndSetTab = (tabNumber: number) => {
    // Validation logic for each tab
    if (tabNumber > openTab) {
      // Moving forward - validate current tab
      switch (openTab) {
        case 1:
          if (!formData.name || !formData.category) {
            toast.error("Please fill in all required fields in Basic Info");
            return;
          }
          break;
        case 2:
          if (!formData.contact_name || !formData.phone_number) {
            toast.error(
              "Please fill in all required fields in Contact Details"
            );
            return;
          }
          break;
        case 3:
          if (
            !formData.country ||
            !formData.city_or_state ||
            !formData.address_physical ||
            !formData.address_mailing
          ) {
            toast.error(
              "Please fill in all required fields in Address & Location"
            );
            return;
          }
          break;
        case 4:
          if (
            !formData.payment_terms ||
            (!formData.tax_vat_16 && !formData.tax_exempt)
          ) {
            toast.error("Please fill in all required fields in Tax & Payment");
            return;
          }
          break;
      }
    }
    // Only validate when moving forward, not when going back
    if (tabNumber < openTab) {
      setOpenTab(tabNumber);
      return;
    }
    setOpenTab(tabNumber);
  };

  return (
    <BaseModal
      size="2xl"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Add Supplier"
      description="Complete all steps to add a new supplier"
      // onStepChange={setCurrentStep}
      // onComplete={handleAddProspect}
    >
      <form onSubmit={handleAddSupplier}>
        <div>
          {/* Tab Navigation */}
          <div className="flex border-b mb-4">
            <button
              type="button"
              className={`px-4 py-2 ${
                openTab === 1 ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => validateAndSetTab(1)}
            >
              Basic Info
            </button>
            <button
              type="button"
              className={`px-4 py-2 ${
                openTab === 2 ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => validateAndSetTab(2)}
            >
              Contact Details
            </button>
            <button
              type="button"
              className={`px-4 py-2 ${
                openTab === 3 ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => validateAndSetTab(3)}
            >
              Address & Location
            </button>
            <button
              type="button"
              className={`px-4 py-2 ${
                openTab === 4 ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => validateAndSetTab(4)}
            >
              Tax & Payment
            </button>
            <button
              type="button"
              className={`px-4 py-2 ${
                openTab === 5 ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => validateAndSetTab(5)}
            >
              Bank Details
            </button>
            <button
              type="button"
              className={`px-4 py-2 ${
                openTab === 6 ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => validateAndSetTab(6)}
            >
              Confirm Details
            </button>
          </div>

          {/* Tab Content */}
          <div className="space-y-4 py-2">
            {/* Tab 1: Basic Info */}
            {openTab === 1 && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="name">Supplier Name*</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter supplier name"
                    required
                  />
                </div>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category*</Label>
                    <CustomSelectField
                      setValue={(e: any) =>
                        handleInputChange({
                          target: { name: "category", value: e },
                        } as any)
                      }
                      useSearchField
                      valueField="id"
                      labelField="name"
                      data={supplierCategories?.data?.results || []}
                      queryFunc={fetchSupplierCategories}
                      loader={loadingSupplierCategories}
                      isMultiple={false}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="branch">Branch</Label>
                    <CustomSelectField
                      setValue={(e: any) =>
                        handleInputChange({
                          target: { name: "branch", value: e },
                        } as any)
                      }
                      useSearchField
                      valueField="branch_code"
                      labelField="name"
                      data={branches || []}
                      queryFunc={fetchBranches}
                      loader={loadingBranches}
                      isMultiple={false}
                    />
                  </div>
                </div>
              </>
            )}

            {/* Tab 2: Contact Details */}
            {openTab === 2 && (
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contact_name">Contact Name*</Label>
                  <Input
                    id="contact_name"
                    name="contact_name"
                    value={formData.contact_name}
                    onChange={handleInputChange}
                    placeholder="Enter contact name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter email address"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone_number">Phone Number*</Label>
                  <Input
                    id="phone_number"
                    name="phone_number"
                    value={formData.phone_number}
                    onChange={handleInputChange}
                    placeholder="Enter phone number"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="alt_phone_number">Alt. Phone Number</Label>
                  <Input
                    id="alt_phone_number"
                    name="alt_phone_number"
                    value={formData.alt_phone_number}
                    onChange={handleInputChange}
                    placeholder="Enter alternate phone number"
                  />
                </div>
              </div>
            )}

            {/* Tab 3: Address & Location */}
            {openTab === 3 && (
              <>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="country">Country*</Label>
                    <Input
                      id="country"
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      placeholder="Enter country"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city_or_state">City / State*</Label>
                    <Input
                      id="city_or_state"
                      name="city_or_state"
                      value={formData.city_or_state}
                      onChange={handleInputChange}
                      placeholder="Enter city or state"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address_physical">Physical Address*</Label>
                  <Textarea
                    id="address_physical"
                    name="address_physical"
                    value={formData.address_physical}
                    onChange={handleInputChange}
                    placeholder="Enter physical address"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address_mailing">Mailing Address*</Label>
                  <Textarea
                    id="address_mailing"
                    name="address_mailing"
                    value={formData.address_mailing}
                    onChange={handleInputChange}
                    placeholder="Enter mailing address"
                    required
                  />
                </div>
              </>
            )}
            {/* Tab 4: Tax & Payment */}
            {openTab === 4 && (
              <>
                <div className="space-y-2">
                  <Label>Tax Status*</Label>
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-2">
                      <input
                        type="radio"
                        id="tax_vat_16"
                        name="tax_status"
                        checked={formData.tax_vat_16}
                        onChange={() => {
                          setformData((prev) => ({
                            ...prev,
                            tax_vat_16: true,
                            tax_exempt: false,
                          }));
                        }}
                      />
                      <Label htmlFor="tax_vat_16">Subject to VAT (16%)</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="radio"
                        id="tax_exempt"
                        name="tax_status"
                        checked={formData.tax_exempt}
                        onChange={() => {
                          setformData((prev) => ({
                            ...prev,
                            tax_vat_16: false,
                            tax_exempt: true,
                          }));
                        }}
                      />
                      <Label htmlFor="tax_exempt">Tax Exempt</Label>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="payment_terms">Payment Terms*</Label>
                  <select
                    id="payment_terms"
                    name="payment_terms"
                    value={formData.payment_terms}
                    onChange={handleInputChange}
                    className="w-full border rounded-md p-2"
                    required
                  >
                    <option value="">Select terms</option>
                    <option value="UPON_DELIVERY">Upon Delivery</option>
                    <option value="15_DAYS">15 Days</option>
                    <option value="30_DAYS">30 Days</option>
                  </select>
                </div>
              </>
            )}

            {/* tab 5 bank details  */}
            {openTab === 5 && (
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bank_name">Bank Name</Label>
                  <Input
                    id="bank_name"
                    name="bank_details.bank_name"
                    value={formData.bank_details?.bank_name || ""}
                    onChange={(e) =>
                      setformData((prev) => ({
                        ...prev,
                        bank_details: {
                          ...prev.bank_details,
                          bank_name: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter bank name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="account_name">Account Name</Label>
                  <Input
                    id="account_name"
                    name="bank_details.account_name"
                    value={formData.bank_details?.account_name || ""}
                    onChange={(e) =>
                      setformData((prev) => ({
                        ...prev,
                        bank_details: {
                          ...prev.bank_details,
                          account_name: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter account name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="account_number">Account Number</Label>
                  <Input
                    id="account_number"
                    name="bank_details.account_number"
                    value={formData.bank_details?.account_number || ""}
                    onChange={(e) =>
                      setformData((prev) => ({
                        ...prev,
                        bank_details: {
                          ...prev.bank_details,
                          account_number: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter account number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="branch_name">Branch Name</Label>
                  <Input
                    id="branch_name"
                    name="bank_details.branch_name"
                    value={formData.bank_details?.branch_name || ""}
                    onChange={(e) =>
                      setformData((prev) => ({
                        ...prev,
                        bank_details: {
                          ...prev.bank_details,
                          branch_name: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter branch name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="swift_code">Swift Code</Label>
                  <Input
                    id="swift_code"
                    name="bank_details.swift_code"
                    value={formData.bank_details?.swift_code || ""}
                    onChange={(e) =>
                      setformData((prev) => ({
                        ...prev,
                        bank_details: {
                          ...prev.bank_details,
                          swift_code: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter swift code"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="swift_iban">Swift IBAN</Label>
                  <Input
                    id="swift_iban"
                    name="bank_details.swift_iban"
                    value={formData.bank_details?.swift_iban || ""}
                    onChange={(e) =>
                      setformData((prev) => ({
                        ...prev,
                        bank_details: {
                          ...prev.bank_details,
                          swift_iban: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter swift IBAN"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bank_code">Bank Code</Label>
                  <Input
                    id="bank_code"
                    name="bank_details.bank_code"
                    value={formData.bank_details?.bank_code || ""}
                    onChange={(e) =>
                      setformData((prev) => ({
                        ...prev,
                        bank_details: {
                          ...prev.bank_details,
                          bank_code: e.target.value,
                        },
                      }))
                    }
                    placeholder="Enter bank code"
                  />
                </div>
              </div>
            )}

            {/* Tab 6: Confirm Details */}
            {openTab === 6 && (
              <>
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">
                    Review Supplier Details
                  </h3>

                  <div className="border rounded-lg p-4 space-y-3">
                    <h4 className="font-medium">Basic Information</h4>
                    <p>Name: {formData.name}</p>
                    <p>Category: {formData.category}</p>
                    <p>Branch: {formData.branch}</p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-3">
                    <h4 className="font-medium">Contact Information</h4>
                    <p>Contact Name: {formData.contact_name}</p>
                    <p>Email: {formData.email}</p>
                    <p>Phone: {formData.phone_number}</p>
                    <p>Alt Phone: {formData.alt_phone_number}</p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-3">
                    <h4 className="font-medium">Address Details</h4>
                    <p>Country: {formData.country}</p>
                    <p>City/State: {formData.city_or_state}</p>
                    <p>Physical Address: {formData.address_physical}</p>
                    <p>Mailing Address: {formData.address_mailing}</p>
                  </div>

                  <div className="border rounded-lg p-4 space-y-3">
                    <h4 className="font-medium">Tax & Payment</h4>
                    <p>VAT (16%): {formData.tax_vat_16 ? "Yes" : "No"}</p>
                    <p>Tax Exempt: {formData.tax_exempt ? "Yes" : "No"}</p>
                    <p>Payment Terms: {formData.payment_terms}</p>
                  </div>

                  {formData.bank_details && (
                    <div className="border rounded-lg p-4 space-y-3">
                      <h4 className="font-medium">Bank Details</h4>
                      <p>Bank Name: {formData.bank_details.bank_name}</p>
                      <p>Account Name: {formData.bank_details.account_name}</p>
                      <p>
                        Account Number: {formData.bank_details.account_number}
                      </p>
                      <p>Branch Name: {formData.bank_details.branch_name}</p>
                    </div>
                  )}
                </div>

                <div className="w-full flex justify-end mt-6">
                  {loading ? (
                    <SpinnerTemp size="sm" type="spinner-double" />
                  ) : (
                    <Button type="submit" variant="default">
                      Confirm and Submit
                      <Send className="ml-2" />
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-6">
            {openTab > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpenTab(openTab - 1)}
              >
                Previous
              </Button>
            )}
            {openTab < 6 && (
              <Button
                type="button"
                className="ml-auto"
                onClick={() => validateAndSetTab(openTab + 1)}
              >
                Next
              </Button>
            )}
          </div>
        </div>
      </form>
    </BaseModal>
  );
};

export default AddSupplier;
