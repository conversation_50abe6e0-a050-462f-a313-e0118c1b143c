import { apiSlice } from "../apiSlice";

export const MenuApi = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        
        getMenus: builder.query({
            query: () => '/menu',
            providesTags: ['Menu']
        }),
        
        
        getMenu: builder.query({
            query: (id) => `/menu/${id}`,
            providesTags: (result, error, id) => [{ type: 'Menu', id }]
        }),
        
       
        createMenu: builder.mutation({
            query: (menuData) => ({
                url: '/menu',
                method: 'POST',
                body: menuData
            }),
            invalidatesTags: ['Menu']
        }),
        
        
        updateMenu: builder.mutation({
            query: ({ id, ...menuData }) => ({
                url: `/menu/${id}`,
                method: 'PUT',
                body: menuData
            }),
            invalidatesTags: (result, error, { id }) => [{ type: 'Menu', id }]
        }),
        
        patchMenu: builder.mutation({
            query: ({ id, ...partialData }) => ({
                url: `/menu/${id}`,
                method: 'PATCH',
                body: partialData
            }),
            invalidatesTags: (result, error, { id }) => [{ type: 'Menu', id }]
        }),
        
       
        deleteMenu: builder.mutation({
            query: (id) => ({
                url: `/menu/${id}`,
                method: 'DELETE'
            }),
            invalidatesTags: ['Menu']
        })
    })
})


export const {
    useGetMenusQuery,
    useGetMenuQuery,
    useCreateMenuMutation,
    useUpdateMenuMutation,
    usePatchMenuMutation,
    useDeleteMenuMutation
} = MenuApi 