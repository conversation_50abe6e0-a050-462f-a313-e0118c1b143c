import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Plus, 
  Trash2, 
  Loader2, 
  Package, 
  Building, 
  Calendar, 
  User,
  AlertTriangle,
  CheckCircle,
  Search
} from "lucide-react";
import {
  useCreateGRNMutation,
  useCreateGRNItemMutation,
  useGetPurchaseOrdersQuery,
  useGetPurchaseOrderQuery,
  useGetStoresQuery,
  useGetUsersQuery,
} from "@/redux/slices/procurement";
import { GRNFormData, GRNItemFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";
import { inventoryIntegrationHooks } from "@/utils/inventoryIntegration";

interface AddGRNProps {
  open: boolean;
  onClose: () => void;
}

const AddGRN = ({ open, onClose }: AddGRNProps) => {
  const [createGRN, { isLoading: creating }] = useCreateGRNMutation();
  const [createGRNItem] = useCreateGRNItemMutation();
  
  // Fetch supporting data
  const { data: purchaseOrders } = useGetPurchaseOrdersQuery({ 
    status: "Approved",
    page_size: 100 
  });
  const { data: stores } = useGetStoresQuery({});
  const { data: users } = useGetUsersQuery({});

  // Selected PO data
  const [selectedPOId, setSelectedPOId] = useState<number | null>(null);
  const { data: selectedPO } = useGetPurchaseOrderQuery(selectedPOId!, { 
    skip: !selectedPOId 
  });

  // Form state
  const [formData, setFormData] = useState<GRNFormData>({
    grn_number: "",
    status: "Partial",
    received_date: new Date().toISOString().split('T')[0],
    purchase_order: 0,
    received_by: 0,
    store: 0,
    notes: "",
    items: [],
  });

  // Generate GRN number and handle URL params
  useEffect(() => {
    if (open) {
      const grnNumber = `GRN-${Date.now()}`;
      setFormData(prev => ({ ...prev, grn_number: grnNumber }));

      // Check if there's a PO ID in URL params
      const urlParams = new URLSearchParams(window.location.search);
      const poId = urlParams.get('po');
      if (poId) {
        const id = parseInt(poId);
        setSelectedPOId(id);
        setFormData(prev => ({ ...prev, purchase_order: id }));
      }
    }
  }, [open]);

  // Update items when PO is selected
  useEffect(() => {
    if (selectedPO && selectedPO.items) {
      const grnItems: GRNItemFormData[] = selectedPO.items.map(item => ({
        product: item.product,
        quantity_received: "0",
        unit_price: item.unit_price,
        unit_of_measure: item.unit_of_measure,
        tax_rate: item.tax_rate || 0,
        notes: "",
        status: "Pending",
      }));
      
      setFormData(prev => ({ 
        ...prev, 
        items: grnItems,
        purchase_order: selectedPO.id!,
        store: selectedPO.delivery_location || 0,
      }));
    }
  }, [selectedPO]);

  const handleInputChange = (field: keyof GRNFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePOSelect = (poId: string) => {
    const id = parseInt(poId);
    setSelectedPOId(id);
    handleInputChange("purchase_order", id);
  };

  const handleItemChange = (index: number, field: keyof GRNItemFormData, value: any) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Calculate total price if quantity or unit price changes
    if (field === "quantity_received" || field === "unit_price") {
      const quantity = parseFloat(field === "quantity_received" ? value : updatedItems[index].quantity_received);
      const unitPrice = parseFloat(field === "unit_price" ? value : updatedItems[index].unit_price);
      
      if (!isNaN(quantity) && !isNaN(unitPrice)) {
        updatedItems[index].total_price = (quantity * unitPrice).toFixed(2);
      }
    }
    
    setFormData(prev => ({ ...prev, items: updatedItems }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.grn_number.trim()) {
      toast.error("GRN number is required");
      return;
    }

    if (!formData.purchase_order) {
      toast.error("Please select a purchase order");
      return;
    }

    if (!formData.received_by) {
      toast.error("Please select who received the goods");
      return;
    }

    if (!formData.store) {
      toast.error("Please select a store");
      return;
    }

    const validItems = formData.items.filter(item => 
      parseFloat(item.quantity_received) > 0
    );

    if (validItems.length === 0) {
      toast.error("Please add at least one item with quantity received");
      return;
    }

    try {
      // Create GRN
      const grnPayload = {
        grn_number: formData.grn_number.trim(),
        status: formData.status,
        received_date: new Date(formData.received_date).toISOString(),
        purchase_order: formData.purchase_order,
        received_by: formData.received_by,
        store: formData.store,
      };

      const result = await createGRN(grnPayload).unwrap();

      // Create GRN items
      if (result.id && validItems.length > 0) {
        const itemPromises = validItems.map(item => {
          const itemPayload = {
            grn: result.id,
            product: item.product,
            quantity_received: item.quantity_received,
            unit_price: item.unit_price,
            total_price: item.total_price,
            unit_of_measure: item.unit_of_measure,
            tax_rate: item.tax_rate,
          };
          return createGRNItem(itemPayload).unwrap();
        });

        const createdItems = await Promise.all(itemPromises);

        // Update inventory if items were created successfully
        try {
          await inventoryIntegrationHooks.onGRNCreated(
            createdItems,
            formData.store,
            result.id,
            formData.received_date
          );
        } catch (inventoryError) {
          console.warn("Inventory update failed:", inventoryError);
          // Don't fail the entire operation if inventory update fails
        }
      }

      toast.success("GRN created successfully");
      handleClose();
    } catch (error: any) {
      console.error("Error creating GRN:", error);
      toast.error(error?.data?.message || "Failed to create GRN");
    }
  };

  const handleClose = () => {
    setFormData({
      grn_number: "",
      status: "Partial",
      received_date: new Date().toISOString().split('T')[0],
      purchase_order: 0,
      received_by: 0,
      store: 0,
      notes: "",
      items: [],
    });
    setSelectedPOId(null);

    // Clear URL params if they exist
    const url = new URL(window.location.href);
    url.searchParams.delete('po');
    window.history.replaceState({}, '', url.toString());

    onClose();
  };

  const getItemStatus = (item: GRNItemFormData) => {
    const received = parseFloat(item.quantity_received);
    if (received === 0) return "Pending";
    return "Received";
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Received":
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="h-3 w-3 mr-1" />Received</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><AlertTriangle className="h-3 w-3 mr-1" />Pending</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };

  // Calculate total received value
  const totalReceivedValue = formData.items.reduce((total, item) => {
    const itemTotal = parseFloat(item.total_price || "0");
    return total + (isNaN(itemTotal) ? 0 : itemTotal);
  }, 0);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create New GRN
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="grn_number">GRN Number *</Label>
                <Input
                  id="grn_number"
                  value={formData.grn_number}
                  onChange={(e) => handleInputChange("grn_number", e.target.value)}
                  placeholder="Enter GRN number"
                  required
                />
              </div>

              <div>
                <Label htmlFor="received_date">Received Date *</Label>
                <Input
                  id="received_date"
                  type="date"
                  value={formData.received_date}
                  onChange={(e) => handleInputChange("received_date", e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="purchase_order">Purchase Order *</Label>
                <Select value={formData.purchase_order.toString()} onValueChange={handlePOSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select purchase order" />
                  </SelectTrigger>
                  <SelectContent>
                    {purchaseOrders?.results?.map((po: any) => (
                      <SelectItem key={po.id} value={po.id.toString()}>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4" />
                          {po.po_number} - {po.supplier_name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="received_by">Received By *</Label>
                <Select value={formData.received_by.toString()} onValueChange={(value) => handleInputChange("received_by", parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select receiver" />
                  </SelectTrigger>
                  <SelectContent>
                    {users?.results?.map((user: any) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {user.first_name} {user.last_name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="store">Store *</Label>
                <Select value={formData.store.toString()} onValueChange={(value) => handleInputChange("store", parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select store" />
                  </SelectTrigger>
                  <SelectContent>
                    {stores?.results?.map((store: any) => (
                      <SelectItem key={store.id} value={store.id.toString()}>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          {store.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value: any) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Partial">Partial</SelectItem>
                    <SelectItem value="Full">Full</SelectItem>
                    <SelectItem value="Rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Items Section */}
          {formData.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Items to Receive</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Ordered Qty</TableHead>
                        <TableHead>Received Qty *</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total Price</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {formData.items.map((item, index) => {
                        const orderedItem = selectedPO?.items?.find(poItem => poItem.product === item.product);
                        return (
                          <TableRow key={index}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{orderedItem?.product_name || `Product ${item.product}`}</p>
                                {orderedItem?.product_code && (
                                  <p className="text-sm text-gray-600">{orderedItem.product_code}</p>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {orderedItem ? parseFloat(orderedItem.quantity).toLocaleString() : "N/A"}
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.quantity_received}
                                onChange={(e) => handleItemChange(index, "quantity_received", e.target.value)}
                                className="w-24"
                                placeholder="0"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.unit_price}
                                onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                                className="w-24"
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              ${item.total_price || "0.00"}
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(getItemStatus(item))}
                            </TableCell>
                            <TableCell>
                              <Input
                                placeholder="Notes..."
                                value={item.notes || ""}
                                onChange={(e) => handleItemChange(index, "notes", e.target.value)}
                                className="w-32"
                              />
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          {formData.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-600">
                      {formData.items.length}
                    </p>
                    <p className="text-sm text-gray-600">Total Items</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {formData.items.filter(item => parseFloat(item.quantity_received) > 0).length}
                    </p>
                    <p className="text-sm text-gray-600">Items Received</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-600">
                      {formData.items.filter(item => parseFloat(item.quantity_received) === 0).length}
                    </p>
                    <p className="text-sm text-gray-600">Pending Items</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600">
                      ${totalReceivedValue.toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-600">Total Value</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Add any additional notes..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Create GRN
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddGRN;
