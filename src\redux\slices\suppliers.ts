import { apiSlice } from "../apiSlice";

export const supplierApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getSupplierCategories: builder.query({
      query: (params) => ({
        url: "/setup/supplier-categories",
        method: "GET",
        params: params,
      }),
      providesTags: ["SupplierCategories"],
    }),

    getSupplierCategory: builder.query({
      query: (id) => ({
        url: `/setup/supplier-categories/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "SupplierCategories", id }],
    }),

    addSupplierCategory: builder.mutation({
      query: (payload) => ({
        url: "/setup/supplier-categories",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["SupplierCategories"],
    }),

    updateSupplierCategory: builder.mutation({
      query: (payload) => ({
        url: `/setup/supplier-categories/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "SupplierCategories", id },
        "SupplierCategories",
      ],
    }),

    getSuppliers: builder.query({
      query: (params) => ({
        url: "/setup/suppliers",
        method: "GET",
        params: params,
      }),
      providesTags: ["Suppliers"],
    }),

    retrieveSupplier: builder.query({
      query: (id) => ({
        url: `/setup/suppliers/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Suppliers", id }],
    }),

    addSuppliers: builder.mutation({
      query: (payload) => ({
        url: "/setup/suppliers",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Suppliers"],
    }),

    patchSuppliers: builder.mutation({
      query: (payload) => ({
        url: `/setup/suppliers/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Suppliers", id },
        "Suppliers",
      ],
    }),

    getSupplierBankDetails: builder.query({
      query: (params) => ({
        url: "/setup/supplier-bank-details",
        method: "GET",
        params: params,
      }),
      providesTags: ["SupplierBankDetails"],
    }),

    retrieveSupplierBankDetail: builder.query({
      query: (id) => ({
        url: `/setup/supplier-bank-details/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [
        { type: "SupplierBankDetails", id },
      ],
    }),

    addSupplierBankDetail: builder.mutation({
      query: (payload) => ({
        url: "/setup/supplier-bank-details",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["SupplierBankDetails"],
    }),

    patchSupplierBankDetail: builder.mutation({
      query: (payload) => ({
        url: `/setup/supplier-bank-details/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "SupplierBankDetails", id },
        "SupplierBankDetails",
      ],
    }),
  }),
});

export const {
  useGetSuppliersQuery,
  useRetrieveSupplierQuery,
  useAddSuppliersMutation,
  usePatchSuppliersMutation,

  useGetSupplierCategoriesQuery,
  useGetSupplierCategoryQuery,
  useAddSupplierCategoryMutation,
  useUpdateSupplierCategoryMutation,

  useGetSupplierBankDetailsQuery,
  useRetrieveSupplierBankDetailQuery,
  useAddSupplierBankDetailMutation,
  usePatchSupplierBankDetailMutation,

  useLazyGetSuppliersQuery,
  useLazyRetrieveSupplierQuery,
  useLazyGetSupplierCategoriesQuery,
  useLazyGetSupplierCategoryQuery,
  useLazyGetSupplierBankDetailsQuery,
  useLazyRetrieveSupplierBankDetailQuery,
} = supplierApiSlice;
