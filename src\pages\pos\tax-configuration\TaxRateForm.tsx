import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X, Percent, Calculator } from 'lucide-react';
import { TaxRateFormData } from '@/types/pos';

interface TaxRateFormProps {
  mode: 'create' | 'edit';
}

// TODO: Replace with actual API call
import { useGetTaxClassesQuery } from '@/redux/slices/taxClasses';

const TaxRateForm: React.FC<TaxRateFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<TaxRateFormData>({
    defaultValues: {
      name: '',
      percentage: 0,
      taxClassId: '',
      isInclusive: false,
    },
  });

  const watchPercentage = form.watch('percentage');
  const watchIsInclusive = form.watch('isInclusive');

  const onSubmit = async (data: TaxRateFormData) => {
    setIsLoading(true);
    try {
      console.log('Submitting tax rate data:', data);
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate('/pos/tax-configuration');
    } catch (error) {
      console.error('Error saving tax rate:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateExample = (baseAmount: number) => {
    if (!watchPercentage) return { taxAmount: 0, totalAmount: baseAmount };
    
    if (watchIsInclusive) {
      const taxAmount = (baseAmount * watchPercentage) / (100 + watchPercentage);
      return {
        taxAmount: Math.round(taxAmount * 100) / 100,
        totalAmount: baseAmount
      };
    } else {
      const taxAmount = (baseAmount * watchPercentage) / 100;
      return {
        taxAmount: Math.round(taxAmount * 100) / 100,
        totalAmount: Math.round((baseAmount + taxAmount) * 100) / 100
      };
    }
  };

  const example = calculateExample(100);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/tax-configuration')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add Tax Rate' : 'Edit Tax Rate'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Create a new tax rate for a tax class'
              : 'Update tax rate information and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Percent className="h-5 w-5" />
                <span>Tax Rate Information</span>
              </CardTitle>
              <CardDescription>
                Enter the details for the tax rate
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="taxClassId"
                rules={{ required: 'Tax class is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Class *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tax class" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockTaxClasses.map((taxClass) => (
                          <SelectItem key={taxClass.id} value={taxClass.id}>
                            {taxClass.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The tax class this rate belongs to
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                rules={{ required: 'Tax rate name is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Rate Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., VAT 16%, Service Tax 12%" {...field} />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for this tax rate
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="percentage"
                rules={{ 
                  required: 'Tax percentage is required',
                  min: { value: 0, message: 'Percentage must be 0 or greater' },
                  max: { value: 100, message: 'Percentage cannot exceed 100%' }
                }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Percentage *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="number" 
                          min="0" 
                          max="100" 
                          step="0.01"
                          placeholder="16.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                        <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormDescription>
                      The tax rate as a percentage (e.g., 16 for 16%)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isInclusive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Inclusive Tax
                      </FormLabel>
                      <FormDescription>
                        Tax is included in the item price (tax-inclusive pricing)
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Tax Calculation Example */}
          {watchPercentage > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calculator className="h-5 w-5" />
                  <span>Tax Calculation Example</span>
                </CardTitle>
                <CardDescription>
                  See how this tax rate will be calculated
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Base Amount:</span>
                      <div className="text-lg font-bold">$100.00</div>
                    </div>
                    <div>
                      <span className="font-medium">Tax Amount ({watchPercentage}%):</span>
                      <div className="text-lg font-bold text-blue-600">
                        ${example.taxAmount.toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium">
                        {watchIsInclusive ? 'Total (Tax Included):' : 'Total (Tax Added):'}
                      </span>
                      <div className="text-lg font-bold text-green-600">
                        ${example.totalAmount.toFixed(2)}
                      </div>
                    </div>
                  </div>
                  <div className="mt-3 text-xs text-muted-foreground">
                    {watchIsInclusive 
                      ? 'With inclusive tax, the tax amount is extracted from the total price.'
                      : 'With exclusive tax, the tax amount is added to the base price.'
                    }
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Usage Information */}
          {mode === 'edit' && (
            <Card>
              <CardHeader>
                <CardTitle>Usage Information</CardTitle>
                <CardDescription>
                  Current usage of this tax rate across the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">15</div>
                    <div className="text-sm text-muted-foreground">Menu Items</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">3</div>
                    <div className="text-sm text-muted-foreground">Revenue Centers</div>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <p className="text-sm text-amber-800">
                    <strong>Warning:</strong> This tax rate is currently in use. Changes may affect existing menu items and transactions.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/pos/tax-configuration')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Tax Rate' : 'Update Tax Rate'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default TaxRateForm;
