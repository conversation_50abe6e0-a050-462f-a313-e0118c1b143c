import { apiSlice } from "../apiSlice";


export const guestCheckApi = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        
        getGuestChecks: builder.query({
            query: () => '/order/guest-check',
            providesTags: ['GuestCheck']
        }),
        
        
        getGuestCheck: builder.query({
            query: (id) => `/order/guest-check/${id}`,
            providesTags: (result, error, id) => [{ type: 'GuestCheck', id }]
        }),
        
       
        createGuestCheck: builder.mutation({
            query: (guestCheckData) => ({
                url: '/order/guest-check',
                method: 'POST',
                body: guestCheckData
            }),
            invalidatesTags: ['GuestCheck']
        }),
        
        
        updateGuestCheck: builder.mutation({
            query: ({ id, ...guestCheckData }) => ({
                url: `/order/guest-check/${id}`,
                method: 'PUT',
                body: guestCheckData
            }),
            invalidatesTags: (result, error, { id }) => [{ type: 'GuestCheck', id }]
        }),
        
        patchGuestCheck: builder.mutation({
            query: ({ id, ...partialData }) => ({
                url: `/order/guest-check/${id}`,
                method: 'PATCH',
                body: partialData
            }),
            invalidatesTags: (result, error, { id }) => [{ type: 'GuestCheck', id }]
        }),
        
       
        deleteGuestCheck: builder.mutation({
            query: (id) => ({
                url: `/order/guest-check/${id}`,
                method: 'DELETE'
            }),
            invalidatesTags: ['GuestCheck']
        })
    })
})


export const {
    useGetGuestChecksQuery,
    useGetGuestCheckQuery,
    useCreateGuestCheckMutation,
    useUpdateGuestCheckMutation,
    usePatchGuestCheckMutation,
    useDeleteGuestCheckMutation
} = guestCheckApi
