import { apiSlice } from "../apiSlice";

export const costCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getOrders: builder.query({
      query: (params) => ({
        url: "order/orders",
        method: "GET",
        params,
        
      }),
    }),

    retrieveOrders: builder.query({
      query: (id) => ({
        url: `/order/orders/${id}`,
        method: "GET",
      }),
    }),

    addTableOrder: builder.mutation({
      query: (payload) => ({
        url: "/order/orders", // Removed ID from URL, as POST typically creates a new resource
        method: "POST",
        body: payload,
      }),
    }),

    deleteOrders: builder.mutation({
      query: (id) => ({
        url: `/order/orders/${id}`,
        method: "DELETE",
      }),
    }),

    patchOrders: builder.mutation({
      query: (payload) => ({
        url: `/order/orders/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
  useLazyGetOrdersQuery,
  usePatchOrdersMutation,
  useLazyRetrieveOrdersQuery,
  useAddTableOrderMutation,
  useDeleteOrdersMutation, 
} = costCenterApiSlice;
