import React, { useState, useEffect } from "react";
import { X, Coffee, Utensils, Clock, Star } from "lucide-react";
import { useCreateMenuMutation } from "@/redux/slices/menuMake";
import { useGetBranchesQuery } from "@/redux/slices";

interface CreateMenuItemModalProps {
  onClose: () => void;
  onSave: (newItem: {
    title: string;
    description: string;
    imageUrl: string;
    price: string;
    category: string;
    rating: number;
    prepTime: string;
  }) => void;
}

export function CreateMenuItemModal({ onClose, onSave }: CreateMenuItemModalProps) {
  
  // Fetch all branches - remove the conditional logic that was causing issues
  const { data: branchData, isLoading: isLoadingBranch, error: branchError } = useGetBranchesQuery();
  const [addFoodOrder, { isLoading: isSubmitting }] = useCreateMenuMutation();
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    availability_type: "ALWAYS",
    available_days: {},
    available_times: {},
    ordering_channels: {},
    is_active: true,
    branch: "",
  });

  // Set default branch when branches are loaded
  useEffect(() => {
    if (branchData && branchData.length > 0 && !formData.branch) {
      setFormData(prev => ({
        ...prev,
        branch: branchData[0].id || branchData[0]._id || ""
      }));
    }
  }, [branchData, formData.branch]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const target = e.target as HTMLInputElement & HTMLSelectElement;
    const { name, value, type } = target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? (target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const payload = {
        name: formData.name,
        description: formData.description,
        availability_type: formData.availability_type,
        available_days: formData.available_days,
        available_times: formData.available_times,
        ordering_channels: formData.ordering_channels,
        is_active: formData.is_active,
        branch: formData.branch,
      };
      
      await addFoodOrder(payload).unwrap();
      
      onSave({
        title: formData.name,
        description: formData.description,
        imageUrl: "", // You may want to add an input for this
        price: "", // You may want to add an input for this
        category: "", // You may want to add an input for this
        rating: 0, // You may want to add an input for this
        prepTime: "", // You may want to add an input for this
      });
      onClose();
    } catch (error) {
      console.error("Failed to add menu item", error);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-full backdrop-blur-sm">
                <Utensils className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Add New Menu Item</h2>
                <p className="text-white/90 text-sm">Fill in the details</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200"
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
              placeholder="Enter menu item name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
              placeholder="Describe your menu item"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Branch</label>
            {isLoadingBranch ? (
              <div className="mt-1 block w-full rounded-md border-gray-300 shadow-sm p-3 bg-gray-50">
                Loading branches...
              </div>
            ) : branchError ? (
              <div className="mt-1 block w-full rounded-md border-red-300 shadow-sm p-3 bg-red-50 text-red-700">
                Error loading branches
              </div>
            ) : (
              <select
                name="branch"
                value={formData.branch}
                onChange={handleChange}
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
              >
                <option value="">Select a branch</option>
                {branchData?.map((branch: any) => (
                  <option key={branch.id} value={branch.id}>
                    {branch.name}
                  </option>
                ))}
              </select>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Availability Type</label>
            <select
              name="availability_type"
              value={formData.availability_type}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
            >
              <option value="ALWAYS">Always Available</option>
              <option value="SCHEDULED">Scheduled Availability</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              id="is_active"
              checked={formData.is_active}
              onChange={handleChange}
              className="mr-2 rounded border-gray-300 text-orange-500 focus:ring-orange-500"
            />
            <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
              Active (available for ordering)
            </label>
          </div>

          {/* Action Buttons */}
          <div className="sticky bottom-0 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 p-4 -mx-6 -mb-6 rounded-b-2xl">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-4 py-2 bg-white border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !formData.branch}
                className="px-6 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-200 hover:shadow-md transform hover:scale-105 disabled:opacity-50 disabled:transform-none disabled:hover:shadow-none"
              >
                {isSubmitting ? "Saving..." : "Save Item"}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}