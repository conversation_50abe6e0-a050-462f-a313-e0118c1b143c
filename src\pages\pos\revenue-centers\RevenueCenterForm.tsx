import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Screen } from '@/app-components/layout/screen';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X } from 'lucide-react';
import { RevenueCenterFormData } from '@/types/pos';
import {
  useCreateRevenueCenterMutation,
  useUpdateRevenueCenterMutation,
  useGetRevenueCenterQuery
} from '@/redux/slices/revenueCenters';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { useEffect } from 'react';

interface RevenueCenterFormProps {
  mode: 'create' | 'edit';
}



const RevenueCenterForm: React.FC<RevenueCenterFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();

  // API hooks
  const [createRevenueCenter, { isLoading: isCreating }] = useCreateRevenueCenterMutation();
  const [updateRevenueCenter, { isLoading: isUpdating }] = useUpdateRevenueCenterMutation();
  const { data: revenueCenterData, isLoading: isLoadingRevenueCenter } = useGetRevenueCenterQuery(id!, {
    skip: mode === 'create' || !id,
  });
  const { data: branches = [], isLoading: loadingBranches } = useGetBranchesQuery({});

  const form = useForm<RevenueCenterFormData>({
    defaultValues: {
      revenue_center_code: '',
      name: '',
      branch: '',
      is_active: true
    },
  });

  // Load revenue center data for edit mode
  useEffect(() => {
    if (mode === 'edit' && revenueCenterData) {
      form.reset({
        revenue_center_code: revenueCenterData.revenue_center_code,
        name: revenueCenterData.name,
        branch: revenueCenterData.branch,
        is_active: revenueCenterData.is_active ?? true,
      });
    }
  }, [revenueCenterData, form, mode]);

  const onSubmit = async (data: RevenueCenterFormData) => {
    try {
      // Validate required fields
      if (!data.name || !data.branch || !data.revenue_center_code) {
        handleApiError({ message: 'Name, revenue center code, and branch are required fields' }, 'validate form');
        return;
      }

      const revenueCenterPayload = {
        revenue_center_code: data.revenue_center_code.trim(),
        name: data.name.trim(),
        branch: data.branch,
        is_active: data.is_active ?? true,
      };

      if (mode === 'create') {
        const result = await createRevenueCenter(revenueCenterPayload).unwrap();
        handleApiSuccess('Revenue center created successfully!', result);
      } else if (mode === 'edit' && id) {
        const result = await updateRevenueCenter({ id, data: revenueCenterPayload }).unwrap();
        handleApiSuccess('Revenue center updated successfully!', result);
      }

      navigate('/pos/revenue-centers');
    } catch (error: any) {
      handleApiError(error, mode === 'create' ? 'create revenue center' : 'update revenue center');
    }
  };

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/revenue-centers')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add Revenue Center' : 'Edit Revenue Center'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Create a new revenue center for a branch'
              : 'Update revenue center information and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 sm:space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the basic details for the revenue center
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="branch"
                rules={{ required: 'Parent branch is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Parent Branch *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select parent branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingBranches ? (
                          <SelectItem value="loading" disabled>Loading branches...</SelectItem>
                        ) : Array.isArray(branches) && branches.length === 0 ? (
                          <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                        ) : Array.isArray(branches) ? (
                          branches.map((branch) => (
                            <SelectItem
                              key={branch.id}
                              value={branch.id?.toString() || `branch-${branch.id}`}
                            >
                              {branch.name} ({branch.branch_code})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The branch this revenue center belongs to
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  rules={{ required: 'Revenue center name is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Revenue Center Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Main Restaurant, Bar & Lounge" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="revenue_center_code"
                  rules={{ required: 'Revenue center code is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Revenue Center Code *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., RC001"
                          maxLength={10}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Unique code for this revenue center (max 10 characters)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Active Status
                      </FormLabel>
                      <FormDescription>
                        Enable or disable this revenue center
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/pos/revenue-centers')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating || isUpdating || isLoadingRevenueCenter}>
              <Save className="h-4 w-4 mr-2" />
              {(isCreating || isUpdating) ? 'Saving...' : mode === 'create' ? 'Create Revenue Center' : 'Update Revenue Center'}
            </Button>
          </div>
        </form>
      </Form>
      </div>
    </Screen>
  );
};

export default RevenueCenterForm;
