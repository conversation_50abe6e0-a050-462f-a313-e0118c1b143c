import { apiSlice } from "../apiSlice";

export const UnitOfMeasureApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getUnitOfMeasure: builder.query({
      query: (params) => ({
        url: "/setup/units-of-measure",
        method: "GET",
        params: params,
      }),
      providesTags: ["UnitOfMeasures"],
    }),

    retrieveUnitOfMeasure: builder.query({
      query: (id) => ({
        url: `/setup/units-of-measure/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "UnitOfMeasures", id }],
    }),

    addUnitOfMeasure: builder.mutation({
      query: (payload) => ({
        url: "/setup/units-of-measure",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["UnitOfMeasures"],
    }),

    patchUnitOfMeasure: builder.mutation({
      query: (payload) => ({
        url: `/setup/units-of-measure/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "UnitOfMeasures", id },
        "UnitOfMeasures",
      ],
    }),
  }),
});

export const {
  useGetUnitOfMeasureQuery,
  useRetrieveUnitOfMeasureQuery,
  useAddUnitOfMeasureMutation,
  usePatchUnitOfMeasureMutation,

  useLazyGetUnitOfMeasureQuery,
  useLazyRetrieveUnitOfMeasureQuery,
} = UnitOfMeasureApiSlice;
