import { PurchaseOrder } from "@/types/procurement";

export interface PDFGenerationOptions {
  includeHeader?: boolean;
  includeFooter?: boolean;
  companyLogo?: string;
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website?: string;
  };
}

export const generatePurchaseOrderHTML = (
  purchaseOrder: PurchaseOrder,
  options: PDFGenerationOptions = {}
): string => {
  const {
    includeHeader = true,
    includeFooter = true,
    companyInfo = {
      name: "GMC Company",
      address: "123 Business Street, City, State 12345",
      phone: "+****************",
      email: "<EMAIL>",
      website: "www.gmccompany.com"
    }
  } = options;

  const formatCurrency = (amount: number | undefined, currency: string = "USD") => {
    if (!amount) return `${currency} 0.00`;
    return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Purchase Order ${purchaseOrder.po_number}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Arial', sans-serif;
          line-height: 1.6;
          color: #333;
          background: white;
        }
        
        .container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          border-bottom: 2px solid #e5e7eb;
          padding-bottom: 20px;
        }
        
        .company-info {
          flex: 1;
        }
        
        .company-name {
          font-size: 24px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 5px;
        }
        
        .company-details {
          font-size: 12px;
          color: #6b7280;
          line-height: 1.4;
        }
        
        .po-info {
          text-align: right;
          flex: 1;
        }
        
        .po-title {
          font-size: 28px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 10px;
        }
        
        .po-number {
          font-size: 18px;
          color: #3b82f6;
          font-weight: 600;
          margin-bottom: 5px;
        }
        
        .po-date {
          font-size: 12px;
          color: #6b7280;
        }
        
        .details-section {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
          margin-bottom: 30px;
        }
        
        .detail-card {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 20px;
          background: #f9fafb;
        }
        
        .detail-title {
          font-size: 16px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 15px;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }
        
        .detail-label {
          font-weight: 600;
          color: #4b5563;
          font-size: 14px;
        }
        
        .detail-value {
          color: #1f2937;
          font-size: 14px;
        }
        
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
        }
        
        .items-table th {
          background: #f3f4f6;
          padding: 12px;
          text-align: left;
          font-weight: 600;
          color: #1f2937;
          border-bottom: 1px solid #e5e7eb;
          font-size: 14px;
        }
        
        .items-table td {
          padding: 12px;
          border-bottom: 1px solid #f3f4f6;
          font-size: 14px;
        }
        
        .items-table tr:last-child td {
          border-bottom: none;
        }
        
        .items-table tr:nth-child(even) {
          background: #f9fafb;
        }
        
        .text-right {
          text-align: right;
        }
        
        .font-medium {
          font-weight: 600;
        }
        
        .summary-section {
          margin-top: 20px;
          border-top: 2px solid #e5e7eb;
          padding-top: 20px;
        }
        
        .summary-table {
          width: 100%;
          max-width: 300px;
          margin-left: auto;
        }
        
        .summary-row {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #f3f4f6;
        }
        
        .summary-row.total {
          border-top: 2px solid #e5e7eb;
          border-bottom: 2px solid #e5e7eb;
          font-weight: bold;
          font-size: 16px;
          color: #1f2937;
          margin-top: 10px;
          padding-top: 15px;
          padding-bottom: 15px;
        }
        
        .notes-section {
          margin-top: 30px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }
        
        .notes-card {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 20px;
          background: #f9fafb;
        }
        
        .notes-title {
          font-size: 16px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 10px;
        }
        
        .notes-content {
          font-size: 14px;
          color: #4b5563;
          line-height: 1.6;
          white-space: pre-wrap;
        }
        
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          text-align: center;
          font-size: 12px;
          color: #6b7280;
        }
        
        .status-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
        }
        
        .status-draft {
          background: #f3f4f6;
          color: #374151;
        }
        
        .status-pending {
          background: #fef3c7;
          color: #92400e;
        }
        
        .status-approved {
          background: #d1fae5;
          color: #065f46;
        }
        
        .status-sent {
          background: #dbeafe;
          color: #1e40af;
        }
        
        .status-cancelled {
          background: #fee2e2;
          color: #991b1b;
        }
        
        @media print {
          .container {
            padding: 0;
          }
          
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        ${includeHeader ? `
        <div class="header">
          <div class="company-info">
            <div class="company-name">${companyInfo.name}</div>
            <div class="company-details">
              ${companyInfo.address}<br>
              Phone: ${companyInfo.phone}<br>
              Email: ${companyInfo.email}
              ${companyInfo.website ? `<br>Website: ${companyInfo.website}` : ''}
            </div>
          </div>
          <div class="po-info">
            <div class="po-title">PURCHASE ORDER</div>
            <div class="po-number">${purchaseOrder.po_number}</div>
            <div class="po-date">Date: ${formatDate(purchaseOrder.created_at)}</div>
            <div class="po-date">
              Status: <span class="status-badge status-${purchaseOrder.status?.toLowerCase().replace(' ', '-')}">${purchaseOrder.status}</span>
            </div>
          </div>
        </div>
        ` : ''}
        
        <div class="details-section">
          <div class="detail-card">
            <div class="detail-title">Supplier Information</div>
            <div class="detail-row">
              <span class="detail-label">Name:</span>
              <span class="detail-value">${purchaseOrder.supplier_name || 'N/A'}</span>
            </div>
            ${purchaseOrder.supplier_email ? `
            <div class="detail-row">
              <span class="detail-label">Email:</span>
              <span class="detail-value">${purchaseOrder.supplier_email}</span>
            </div>
            ` : ''}
            ${purchaseOrder.supplier_phone ? `
            <div class="detail-row">
              <span class="detail-label">Phone:</span>
              <span class="detail-value">${purchaseOrder.supplier_phone}</span>
            </div>
            ` : ''}
            ${purchaseOrder.supplier_address ? `
            <div class="detail-row">
              <span class="detail-label">Address:</span>
              <span class="detail-value">${purchaseOrder.supplier_address}</span>
            </div>
            ` : ''}
          </div>
          
          <div class="detail-card">
            <div class="detail-title">Delivery Information</div>
            <div class="detail-row">
              <span class="detail-label">Location:</span>
              <span class="detail-value">${purchaseOrder.delivery_location_name || 'N/A'}</span>
            </div>
            ${purchaseOrder.delivery_address ? `
            <div class="detail-row">
              <span class="detail-label">Address:</span>
              <span class="detail-value">${purchaseOrder.delivery_address}</span>
            </div>
            ` : ''}
            <div class="detail-row">
              <span class="detail-label">Expected Date:</span>
              <span class="detail-value">${formatDate(purchaseOrder.delivery_date)}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payment Terms:</span>
              <span class="detail-value">${purchaseOrder.payment_terms || 'N/A'}</span>
            </div>
          </div>
        </div>
        
        <table class="items-table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Quantity</th>
              <th>Unit</th>
              <th>Unit Price</th>
              <th class="text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            ${purchaseOrder.items?.map(item => `
              <tr>
                <td class="font-medium">${item.product_name || 'N/A'}</td>
                <td>${item.quantity || 0}</td>
                <td>${item.unit_of_measure_name || 'N/A'}</td>
                <td>${formatCurrency(item.unit_price, purchaseOrder.currency)}</td>
                <td class="text-right font-medium">
                  ${formatCurrency((item.quantity || 0) * (item.unit_price || 0), purchaseOrder.currency)}
                </td>
              </tr>
            `).join('') || '<tr><td colspan="5" class="text-center">No items</td></tr>'}
          </tbody>
        </table>
        
        <div class="summary-section">
          <div class="summary-table">
            <div class="summary-row">
              <span>Subtotal:</span>
              <span class="font-medium">${formatCurrency(purchaseOrder.subtotal, purchaseOrder.currency)}</span>
            </div>
            ${purchaseOrder.tax_amount ? `
            <div class="summary-row">
              <span>Tax (${purchaseOrder.tax_rate || 0}%):</span>
              <span class="font-medium">${formatCurrency(purchaseOrder.tax_amount, purchaseOrder.currency)}</span>
            </div>
            ` : ''}
            <div class="summary-row total">
              <span>Total:</span>
              <span>${formatCurrency(purchaseOrder.total_value, purchaseOrder.currency)}</span>
            </div>
          </div>
        </div>
        
        ${(purchaseOrder.notes || purchaseOrder.terms_and_conditions) ? `
        <div class="notes-section">
          ${purchaseOrder.notes ? `
          <div class="notes-card">
            <div class="notes-title">Notes</div>
            <div class="notes-content">${purchaseOrder.notes}</div>
          </div>
          ` : ''}
          
          ${purchaseOrder.terms_and_conditions ? `
          <div class="notes-card">
            <div class="notes-title">Terms and Conditions</div>
            <div class="notes-content">${purchaseOrder.terms_and_conditions}</div>
          </div>
          ` : ''}
        </div>
        ` : ''}
        
        ${includeFooter ? `
        <div class="footer">
          <p>This is a computer-generated document. No signature is required.</p>
          <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        ` : ''}
      </div>
    </body>
    </html>
  `;
};

export const downloadPurchaseOrderPDF = async (
  purchaseOrder: PurchaseOrder,
  options?: PDFGenerationOptions
) => {
  const htmlContent = generatePurchaseOrderHTML(purchaseOrder, options);
  
  // Create a blob with the HTML content
  const blob = new Blob([htmlContent], { type: 'text/html' });
  const url = URL.createObjectURL(blob);
  
  // Create a temporary link and trigger download
  const link = document.createElement('a');
  link.href = url;
  link.download = `purchase-order-${purchaseOrder.po_number || 'draft'}.html`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  URL.revokeObjectURL(url);
};

export const printPurchaseOrder = (
  purchaseOrder: PurchaseOrder,
  options?: PDFGenerationOptions
) => {
  const htmlContent = generatePurchaseOrderHTML(purchaseOrder, options);
  
  // Open a new window for printing
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }
};
