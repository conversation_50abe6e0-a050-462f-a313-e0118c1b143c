import { apiSlice } from "../apiSlice";

export const StoreApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getStores: builder.query({
      query: (params) => ({
        url: "/setup/stores",
        method: "GET",
        params: params,
      }),
      providesTags: ["Stores"],
    }),

    retrieveStore: builder.query({
      query: (id) => ({
        url: `/setup/stores/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Stores", id }],
    }),

    addStores: builder.mutation({
      query: (payload) => ({
        url: "/setup/stores",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Stores"],
    }),

    patchStores: builder.mutation({
      query: (payload) => ({
        url: `/setup/stores/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Stores", id },
        "Stores",
      ],
    }),
  }),
});

export const {
  useGetStoresQuery,
  useRetrieveStoreQuery,
  useAddStoresMutation,
  usePatchStoresMutation,

  useLazyGetStoresQuery,
  useLazyRetrieveStoreQuery,
} = StoreApiSlice;
