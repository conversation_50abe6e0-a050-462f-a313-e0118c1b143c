import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FileText, TrendingUp, AlertCircle, Plus, Trash2 } from "lucide-react";
import {
  useCreateBidAnalysisMutation,
  useCreateBidAnalysisLineMutation,
  useGetRFQsQuery,
  useGetRFQResponsesQuery,
  useGetSuppliersQuery,
} from "@/redux/slices/procurement";
import { BidAnalysisFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddBidAnalysisProps {
  open: boolean;
  onClose: () => void;
}

const AddBidAnalysis = ({ open, onClose }: AddBidAnalysisProps) => {
  const [createBidAnalysis, { isLoading: creating }] = useCreateBidAnalysisMutation();
  const [createBidAnalysisLine] = useCreateBidAnalysisLineMutation();

  // Fetch supporting data
  const { data: rfqs } = useGetRFQsQuery({ status: "Closed" });
  const { data: suppliers } = useGetSuppliersQuery({});

  const [formData, setFormData] = useState<BidAnalysisFormData>({
    split_award: false,
    recommendation_notes: "",
    finalized_at: new Date().toISOString(),
    rfq: "",
    created_by: "",
    selected_responses: [],
    lines: [],
  });

  const [selectedRFQ, setSelectedRFQ] = useState<any>(null);

  // Fetch RFQ responses when RFQ is selected
  const { data: rfqResponses } = useGetRFQResponsesQuery(
    { rfq: formData.rfq },
    { skip: !formData.rfq }
  );

  const handleInputChange = (field: keyof BidAnalysisFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleLineChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      lines: prev.lines.map((line, i) =>
        i === index ? { ...line, [field]: value } : line
      ),
    }));
  };

  const addLine = () => {
    setFormData((prev) => ({
      ...prev,
      lines: [
        ...prev.lines,
        {
          unit_price: "",
          quantity_awarded: "",
          delivery_time_days: undefined,
          currency: "KES",
          rfq_item: "",
          supplier: "",
        },
      ],
    }));
  };

  const removeLine = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      lines: prev.lines.filter((_, i) => i !== index),
    }));
  };

  const handleRFQChange = (rfqId: string) => {
    const rfqIdNum = parseInt(rfqId);
    const rfq = rfqs?.data?.results?.find((r: any) => r.id === rfqIdNum);

    setSelectedRFQ(rfq);
    handleInputChange("rfq", rfqIdNum);
  };

  const resetForm = () => {
    setFormData({
      split_award: false,
      recommendation_notes: "",
      finalized_at: new Date().toISOString(),
      rfq: "",
      created_by: "",
      selected_responses: [],
      lines: [],
    });
    setSelectedRFQ(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.rfq) {
      toast.error("Please select an RFQ");
      return;
    }

    if (!formData.recommendation_notes.trim()) {
      toast.error("Please enter recommendation notes");
      return;
    }

    if (!formData.created_by) {
      toast.error("Please enter created by user ID");
      return;
    }

    if (formData.selected_responses.length === 0) {
      toast.error("Please select at least one RFQ response");
      return;
    }

    try {
      const payload = {
        split_award: formData.split_award,
        recommendation_notes: formData.recommendation_notes.trim(),
        finalized_at: formData.finalized_at,
        rfq: Number(formData.rfq),
        created_by: Number(formData.created_by),
        selected_responses: formData.selected_responses,
      };

      const result = await createBidAnalysis(payload).unwrap();

      // Create lines separately if the bid analysis was created successfully
      if (result.id && formData.lines.length > 0) {
        for (const line of formData.lines) {
          if (line.unit_price && line.quantity_awarded && line.rfq_item && line.supplier) {
            await createBidAnalysisLine({
              unit_price: line.unit_price,
              quantity_awarded: line.quantity_awarded,
              delivery_time_days: line.delivery_time_days || null,
              currency: line.currency,
              bid_analysis: result.id,
              rfq_item: Number(line.rfq_item),
              supplier: Number(line.supplier),
            }).unwrap();
          }
        }
      }

      toast.success("Bid analysis created successfully");
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create bid analysis");
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const responseCount = rfqResponses?.data?.results?.length || 0;
  const supplierNames = rfqResponses?.data?.results?.map((r: any) => r.supplier_name) || [];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Create Bid Analysis
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="h-4 w-4" />
                Bid Analysis Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="rfq">RFQ *</Label>
                  <Select
                    value={formData.rfq ? formData.rfq.toString() : ""}
                    onValueChange={handleRFQChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select RFQ to analyze" />
                    </SelectTrigger>
                    <SelectContent>
                      {rfqs?.data?.results?.map((rfq: any) => (
                        <SelectItem key={rfq.id} value={rfq.id.toString()}>
                          <div className="flex flex-col">
                            <span className="font-medium">{rfq.rfq_number}</span>
                            <span className="text-sm text-gray-500">
                              Created {new Date(rfq.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500 mt-1">
                    Select an RFQ to create bid analysis for
                  </p>
                </div>

                <div>
                  <Label htmlFor="created_by">Created By (User ID) *</Label>
                  <Input
                    id="created_by"
                    type="number"
                    value={formData.created_by}
                    onChange={(e) => handleInputChange("created_by", e.target.value)}
                    placeholder="Enter user ID"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    ID of the user creating this analysis
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="finalized_at">Finalized At *</Label>
                  <Input
                    id="finalized_at"
                    type="datetime-local"
                    value={formData.finalized_at.slice(0, 16)}
                    onChange={(e) => handleInputChange("finalized_at", e.target.value + ":00.000Z")}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    When this analysis was finalized
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="split_award"
                    checked={formData.split_award}
                    onCheckedChange={(checked) => handleInputChange("split_award", checked)}
                  />
                  <Label htmlFor="split_award">Split Award</Label>
                  <p className="text-sm text-gray-500">
                    Allow awarding different items to different suppliers
                  </p>
                </div>
              </div>

              <div>
                <Label htmlFor="recommendation_notes">Recommendation Notes *</Label>
                <Textarea
                  id="recommendation_notes"
                  value={formData.recommendation_notes}
                  onChange={(e) => handleInputChange("recommendation_notes", e.target.value)}
                  placeholder="Enter detailed recommendation notes..."
                  rows={4}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Detailed notes about the bid analysis and recommendations (required)
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Selected Responses */}
          {formData.rfq && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Selected RFQ Responses</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label>Select RFQ Responses to Include *</Label>
                  <div className="space-y-2 mt-2">
                    {rfqResponses?.data?.results?.map((response: any) => (
                      <div key={response.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`response-${response.id}`}
                          checked={formData.selected_responses.includes(response.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              handleInputChange("selected_responses", [...formData.selected_responses, response.id]);
                            } else {
                              handleInputChange("selected_responses", formData.selected_responses.filter(id => id !== response.id));
                            }
                          }}
                        />
                        <Label htmlFor={`response-${response.id}`} className="flex-1">
                          <div className="flex justify-between items-center">
                            <span>{response.supplier_name || `Supplier ${response.supplier}`}</span>
                            <span className="text-sm text-gray-500">
                              Submitted: {new Date(response.submitted_at).toLocaleDateString()}
                            </span>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    Select which RFQ responses to include in this bid analysis
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bid Analysis Lines */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg">
                <span>Bid Analysis Lines</span>
                <Button type="button" onClick={addLine} size="sm">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Line
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {formData.lines.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No analysis lines added yet.</p>
                  <p className="text-sm">Click "Add Line" to start adding bid analysis lines.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {formData.lines.map((line, index) => (
                    <div key={index} className="p-4 border rounded-lg space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Line {index + 1}</h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLine(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                          <Label>Unit Price *</Label>
                          <Input
                            placeholder="Enter unit price (decimal as string)"
                            value={line.unit_price}
                            onChange={(e) => handleLineChange(index, "unit_price", e.target.value)}
                          />
                        </div>

                        <div>
                          <Label>Quantity Awarded *</Label>
                          <Input
                            placeholder="Enter quantity awarded (decimal as string)"
                            value={line.quantity_awarded}
                            onChange={(e) => handleLineChange(index, "quantity_awarded", e.target.value)}
                          />
                        </div>

                        <div>
                          <Label>Currency *</Label>
                          <Select
                            value={line.currency}
                            onValueChange={(value) => handleLineChange(index, "currency", value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                              <SelectItem value="USD">USD - US Dollar</SelectItem>
                              <SelectItem value="EUR">EUR - Euro</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label>RFQ Item *</Label>
                          <Input
                            type="number"
                            placeholder="Enter RFQ item ID"
                            value={line.rfq_item}
                            onChange={(e) => handleLineChange(index, "rfq_item", e.target.value)}
                          />
                        </div>

                        <div>
                          <Label>Supplier *</Label>
                          <Select
                            value={line.supplier ? line.supplier.toString() : ""}
                            onValueChange={(value) => handleLineChange(index, "supplier", value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select supplier" />
                            </SelectTrigger>
                            <SelectContent>
                              {suppliers?.data?.results?.map((supplier: any) => (
                                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                                  {supplier.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label>Delivery Time (Days)</Label>
                          <Input
                            type="number"
                            placeholder="Enter delivery time in days"
                            value={line.delivery_time_days || ""}
                            onChange={(e) => handleLineChange(index, "delivery_time_days", parseInt(e.target.value) || undefined)}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={creating || !formData.rfq || !formData.recommendation_notes.trim() || !formData.created_by || formData.selected_responses.length === 0}
            >
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Bid Analysis...
                </>
              ) : (
                "Create Bid Analysis"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddBidAnalysis;
