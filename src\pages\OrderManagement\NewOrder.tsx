// src/pages/OrderManagement/NewOrder.tsx
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import BaseModal from "@/components/custom/modals/BaseModal";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useGetWorkstationsQuery } from "@/redux/slices";
import { useAddTableOrderMutation } from "@/redux/slices/order";
import { useGetUsersQuery } from "@/redux/slices/users";
import { useGetTablesQuery } from "@/redux/slices/tables";
import { 
  Users, MapPin, DollarSign, User, Monitor, Building, FileText,
  Utensils, Car, Home, Truck, Globe, Phone
} from "lucide-react";

// Types
interface User {
  employee_no: string | number;
  full_name: string;
}

interface Workstation {
  id: number;
  Workstation_code: string;
  name: string;
  is_active: boolean;
}

interface Table {
  table_number: string;
  capacity: number;
  is_active: boolean;
}

interface ExtendedCheckInForm {
  orderNumber: string;
  order_type: "DINE_IN" | "TAKEAWAY" | "DELIVERY" | "ROOM_SERVICE" | "DRIVE_THRU" | "ONLINE";
  status: "OPEN" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED" | "PENDING";
  total_amount: string;
  payment_status: boolean;
  guest_count: number;
  tax_amount: string;
  service_charge: string;
  catering_levy: string;
  revenue_center: number;
  workstation: number;
  table_number: number | null;
  created_by: string | null;
  specialRequests: string;
}

interface CheckInModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  checkInForm: ExtendedCheckInForm;
  setCheckInForm: React.Dispatch<React.SetStateAction<ExtendedCheckInForm>>;
}

// Constants
const dummyRevenueCenters = [
  { id: 1, name: "Kitengela Main", is_active: true },
  { id: 2, name: "Kitengela Bar", is_active: true },
  { id: 3, name: "Karen Main", is_active: true },
  { id: 5, name: "Nairobi Central", is_active: true },
];

const orderTypeConfig = {
  DINE_IN: { icon: Utensils, color: "from-primary to-secondary" },
  TAKEAWAY: { icon: Car, color: "from-primary to-secondary" },
  DELIVERY: { icon: Truck, color: "from-primary to-secondary" },
  ROOM_SERVICE: { icon: Home, color: "from-primary to-secondary" },
  DRIVE_THRU: { icon: Car, color: "from-primary to-secondary" },
  ONLINE: { icon: Globe, color: "from-primary to-secondary" },
};

const statusConfig = {
  OPEN: "from-primary to-secondary",
  IN_PROGRESS: "from-yellow-500 to-orange-500",
  COMPLETED: "from-green-500 to-emerald-600",
  CANCELLED: "from-red-500 to-red-600",
  PENDING: "from-gray-500 to-gray-600",
};

// Utilities
const generateOrderNumber = () => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, "0");
  return `ORD-${timestamp}-${random}`;
};

const extractArrayFromResponse = (data: any, paths: string[]) => {
  if (!data) return [];
  if (Array.isArray(data)) return data;
  
  for (const path of paths) {
    const value = path.split('.').reduce((obj, key) => obj?.[key], data);
    if (Array.isArray(value)) return value;
  }
  return [];
};

export const CheckInModal: React.FC<CheckInModalProps> = ({
  open,
  onOpenChange,
  checkInForm,
  setCheckInForm,
}) => {
  const { data: tablesData, isLoading: loadingTables } = useGetTablesQuery({});
  const [addTableOrder, { isLoading: isSubmitting }] = useAddTableOrderMutation();
  const { data: usersData, isLoading: loadingUsers, error: usersError } = useGetUsersQuery({});
  const { data: workstationsData, isLoading: loadingWorkstations, error: workstationsError } = useGetWorkstationsQuery({});

  // Memoized options
  const userOptions = React.useMemo(() => {
    const users = extractArrayFromResponse(usersData, ['data.results', 'data.users', 'data', 'results', 'users']);
    return users
      .filter((user: User) => user.employee_no && user.full_name)
      .map((user: User) => ({
        value: user.employee_no.toString(),
        label: user.full_name,
      }));
  }, [usersData]);

  const tableOptions = React.useMemo(() => {
    const tables = extractArrayFromResponse(tablesData, ['data.results', 'data.tables', 'data', 'results', 'tables']);
    return tables
      .filter((table: Table) => table.is_active && table.table_number)
      .map((table: Table) => ({
        value: table.table_number,
        label: `Table ${table.table_number} (Capacity: ${table.capacity})`,
      }))
      .sort((a, b) => parseInt(a.value) - parseInt(b.value));
  }, [tablesData]);

  const workstations = React.useMemo(() => {
    const ws = extractArrayFromResponse(workstationsData, ['data.results', 'data', 'results']);
    return ws.filter((w: Workstation) => w.is_active);
  }, [workstationsData]);

  // Auto-generate order number
  React.useEffect(() => {
    if (open && !checkInForm.orderNumber) {
      setCheckInForm((prev) => ({ ...prev, orderNumber: generateOrderNumber() }));
    }
  }, [open, setCheckInForm, checkInForm.orderNumber]);

  const handleCheckIn = async () => {
    try {
      await addTableOrder({
        order_number: checkInForm.orderNumber,
        order_type: checkInForm.order_type,
        status: checkInForm.status,
        total_amount: checkInForm.total_amount,
        payment_status: checkInForm.payment_status,
        guest_count: checkInForm.guest_count,
        tax_amount: checkInForm.tax_amount,
        service_charge: checkInForm.service_charge,
        catering_levy: checkInForm.catering_levy,
        revenue_center: checkInForm.revenue_center,
        workstation: checkInForm.workstation,
        table_number: checkInForm.table_number,
        created_by: checkInForm.created_by,
        special_requests: checkInForm.specialRequests,
      }).unwrap();
      onOpenChange(false);
    } catch (error) {
      console.error("Check-in failed", error);
    }
  };

  // Components
  const FormSection = ({ title, icon: Icon, children, className = "" }: {
    title: string;
    icon: any;
    children: React.ReactNode;
    className?: string;
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "p-6 bg-gradient-to-br from-white/80 to-gray-50/80 backdrop-blur-sm",
        "border border-gray-200/50 rounded-2xl shadow-lg hover:shadow-xl",
        "transition-all duration-300 hover:scale-[1.02]",
        className
      )}
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
          <Icon className="w-5 h-5 text-white" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
      </div>
      <div className="space-y-4">{children}</div>
    </motion.div>
  );

  const FormField = ({ label, icon: Icon, children, required = false }: {
    label: string;
    icon?: any;
    children: React.ReactNode;
    required?: boolean;
  }) => (
    <div className="space-y-2">
      <Label className="flex items-center gap-2 text-sm font-medium text-gray-700">
        {Icon && <Icon className="w-4 h-4 text-gray-500" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      {children}
    </div>
  );

  const OrderTypeCard = ({ type, isSelected, onClick }: {
    type: ExtendedCheckInForm["order_type"];
    isSelected: boolean;
    onClick: () => void;
  }) => {
    const { icon: Icon, color } = orderTypeConfig[type];
    
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={onClick}
        className={cn(
          "p-4 rounded-xl cursor-pointer transition-all duration-300 border-2",
          isSelected
            ? `border-blue-500 bg-gradient-to-br ${color} text-white shadow-lg`
            : "border-gray-200 bg-white/50 hover:border-gray-300"
        )}
      >
        <div className="flex flex-col items-center gap-2">
          <Icon className="w-6 h-6" />
          <span className="text-xs font-medium">{type.replace('_', ' ')}</span>
        </div>
      </motion.div>
    );
  };

  const StatusBadge = ({ status, isSelected, onClick }: {
    status: ExtendedCheckInForm["status"];
    isSelected: boolean;
    onClick: () => void;
  }) => (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={cn(
        "px-3 py-2 rounded-lg cursor-pointer transition-all text-sm font-medium",
        isSelected
          ? `bg-gradient-to-r ${statusConfig[status]} text-white shadow-lg`
          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
      )}
    >
      {status.replace('_', ' ')}
    </motion.div>
  );

  const isFormValid = () => {
    return (
      checkInForm.order_type &&
      checkInForm.status &&
      checkInForm.guest_count > 0 &&
      checkInForm.guest_count <= 4294967295 &&
      checkInForm.created_by &&
      checkInForm.total_amount &&
      checkInForm.tax_amount &&
      checkInForm.service_charge &&
      checkInForm.catering_levy &&
      checkInForm.revenue_center &&
      checkInForm.workstation > 0 &&
      (checkInForm.order_type !== "DINE_IN" || checkInForm.table_number) &&
      !loadingWorkstations
    );
  };

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title=""
      description=""
      className="max-w-4xl"
      size="xl"
    >
      <div className="relative overflow-hidden">
        {/* Header */}
        <div className="p-6 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h2 className="text-2xl font-bold mb-2">Create New Order</h2>
            <p className="text-blue-100">Fill in the details to process a new guest order</p>
          </motion.div>
        </div>

        <div className="p-6 bg-gradient-to-br from-gray-50 to-white">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              <FormSection title="Order Information" icon={FileText}>
                <FormField label="Order Number" icon={Phone}>
                  <Input
                    value={checkInForm.orderNumber}
                    readOnly
                    className="bg-gradient-to-r from-gray-50 to-gray-100 border-0 font-mono text-center font-semibold"
                  />
                </FormField>

                <FormField label="Order Type" required>
                  <div className="grid grid-cols-3 gap-2">
                    {Object.keys(orderTypeConfig).map((type) => (
                      <OrderTypeCard
                        key={type}
                        type={type as ExtendedCheckInForm["order_type"]}
                        isSelected={checkInForm.order_type === type}
                        onClick={() => setCheckInForm({
                          ...checkInForm,
                          order_type: type as ExtendedCheckInForm["order_type"],
                          table_number: type !== "DINE_IN" ? null : checkInForm.table_number,
                        })}
                      />
                    ))}
                  </div>
                </FormField>

                <FormField label="Order Status" required>
                  <div className="flex flex-wrap gap-2">
                    {Object.keys(statusConfig).map((status) => (
                      <StatusBadge
                        key={status}
                        status={status as ExtendedCheckInForm["status"]}
                        isSelected={checkInForm.status === status}
                        onClick={() => setCheckInForm({ 
                          ...checkInForm, 
                          status: status as ExtendedCheckInForm["status"] 
                        })}
                      />
                    ))}
                  </div>
                </FormField>
              </FormSection>

              <AnimatePresence>
                {checkInForm.order_type === "DINE_IN" && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                  >
                    <FormSection title="Table Selection" icon={MapPin} className="border-l-4 border-l-emerald-500">
                      <FormField label="Table Number" icon={MapPin} required>
                        <Select
                          value={checkInForm.table_number?.toString() || ""}
                          onValueChange={(value) =>
                            setCheckInForm({ ...checkInForm, table_number: value ? parseInt(value) : null })
                          }
                        >
                          <SelectTrigger className="bg-white border-gray-200">
                            <SelectValue placeholder="Choose a table" />
                          </SelectTrigger>
                          <SelectContent>
                            {loadingTables ? (
                              <SelectItem value="loading" disabled>Loading tables...</SelectItem>
                            ) : tableOptions.length > 0 ? (
                              tableOptions.map((table) => (
                                <SelectItem key={table.value} value={table.value}>
                                  <div className="flex items-center gap-2">
                                    <MapPin className="w-4 h-4" />
                                    {table.label}
                                  </div>
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value="no-tables" disabled>No active tables available</SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </FormField>
                    </FormSection>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              <FormSection title="Guest & Staff Details" icon={Users}>
                <FormField label="Number of Guests" icon={Users} required>
                  <Input
                    type="number"
                    placeholder="Enter guest count"
                    value={checkInForm.guest_count.toString()}
                    onChange={(e) =>
                      setCheckInForm({
                        ...checkInForm,
                        guest_count: parseInt(e.target.value) || 0,
                      })
                    }
                    min="0"
                    max="4294967295"
                    className="bg-white border-gray-200"
                  />
                </FormField>

                <FormField label="Assigned Staff" icon={User} required>
                  <Select
                    value={checkInForm.created_by || ""}
                    onValueChange={(value) =>
                      setCheckInForm({ ...checkInForm, created_by: value || null })
                    }
                  >
                    <SelectTrigger className="bg-white border-gray-200">
                      <SelectValue placeholder="Select staff member" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingUsers ? (
                        <SelectItem value="loading" disabled>Loading staff...</SelectItem>
                      ) : usersError ? (
                        <SelectItem value="error" disabled>Error loading staff</SelectItem>
                      ) : userOptions.length > 0 ? (
                        userOptions.map((user) => (
                          <SelectItem key={user.value} value={user.value}>
                            <div className="flex items-center gap-2">
                              <User className="w-4 h-4" />
                              {user.label}
                            </div>
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-users" disabled>No staff available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </FormField>
              </FormSection>

              <FormSection title="Financial Details" icon={DollarSign}>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { label: "Total Amount", key: "total_amount" },
                    { label: "Tax Amount", key: "tax_amount" },
                    { label: "Service Charge", key: "service_charge" },
                    { label: "Catering Levy", key: "catering_levy" },
                  ].map(({ label, key }) => (
                    <FormField key={key} label={label} required>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        value={checkInForm[key as keyof ExtendedCheckInForm] as string}
                        onChange={(e) => setCheckInForm({ 
                          ...checkInForm, 
                          [key]: e.target.value 
                        })}
                        className="bg-white border-gray-200"
                      />
                    </FormField>
                  ))}
                </div>
              </FormSection>

              <FormSection title="System Details" icon={Monitor}>
                <FormField label="Revenue Center" icon={Building} required>
                  <Select
                    value={checkInForm.revenue_center.toString()}
                    onValueChange={(value) =>
                      setCheckInForm({
                        ...checkInForm,
                        revenue_center: parseInt(value) || 0,
                      })
                    }
                  >
                    <SelectTrigger className="bg-white border-gray-200">
                      <SelectValue placeholder="Select revenue center" />
                    </SelectTrigger>
                    <SelectContent>
                      {dummyRevenueCenters.map((rc) => (
                        <SelectItem key={rc.id} value={rc.id.toString()}>
                          <div className="flex items-center gap-2">
                            <Building className="w-4 h-4" />
                            {rc.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormField>

                <FormField label="Workstation" icon={Monitor} required>
                  {loadingWorkstations ? (
                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                      <span>Loading workstations...</span>
                    </div>
                  ) : workstationsError ? (
                    <div className="text-red-500 p-3 bg-red-50 rounded-lg">Error loading workstations</div>
                  ) : (
                    <Select
                      value={checkInForm.workstation.toString()}
                      onValueChange={(value) =>
                        setCheckInForm({
                          ...checkInForm,
                          workstation: parseInt(value) || 0,
                        })
                      }
                    >
                      <SelectTrigger className="bg-white border-gray-200">
                        <SelectValue placeholder="Select workstation" />
                      </SelectTrigger>
                      <SelectContent>
                        {workstations.map((ws: Workstation) => (
                          <SelectItem key={ws.id} value={ws.id.toString()}>
                            <div className="flex items-center gap-2">
                              <Monitor className="w-4 h-4" />
                              <div>
                                <div className="font-medium">{ws.name}</div>
                                <div className="text-xs text-gray-500">Code: {ws.Workstation_code}</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </FormField>

                <FormField label="Special Requests" icon={FileText}>
                  <Input
                    placeholder="Any special requests or notes..."
                    value={checkInForm.specialRequests}
                    onChange={(e) => setCheckInForm({ ...checkInForm, specialRequests: e.target.value })}
                    className="bg-white border-gray-200"
                  />
                </FormField>
              </FormSection>
            </div>
          </div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200"
          >
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="px-6 py-3"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCheckIn}
              disabled={!isFormValid() || isSubmitting}
              className={cn(
                "px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600",
                "hover:from-blue-700 hover:to-purple-700",
                "disabled:from-gray-400 disabled:to-gray-500",
                "transform transition-all duration-200",
                "shadow-lg hover:shadow-xl hover:scale-105",
                "text-white font-semibold"
              )}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Processing...
                </div>
              ) : (
                "Create Order"
              )}
            </Button>
          </motion.div>
        </div>
      </div>
    </BaseModal>
  );
};