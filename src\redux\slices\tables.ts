
import { apiSlice } from "../apiSlice";


export const userApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // ── Users ────────────────────────────────────────────────────────────────
    getTables: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `order/tables`,
        method: "GET",
        params,
       
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data ?? [];
      // },
      providesTags: ["Tables"],
    }),
    getUserDetails: builder.query<any, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "GET",
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
    createUser: builder.mutation<any, Partial<any>>({
      query: (data) => ({
        url: `/users/users`,
        method: "POST",
        body: data,
        
      }),
      invalidatesTags: ["Users"],
    }),
    updateUserDetails: builder.mutation<
      any,
      { user_id: number } & Partial<any>
    >({
      query: ({ user_id, ...patch }) => ({
        url: `/users/users/${user_id}`,
        method: "PATCH",
        body: patch,
        
      }),
      invalidatesTags: ["Users"],
    }),
    deleteUser: builder.mutation<any, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "DELETE",
        
      }),
      invalidatesTags: ["Users"],
    }),

    // ── Departments ─────────────────────────────────────────────────────────
    getDepartments: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `/users/departments`,
        method: "GET",
        params,
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
    getDepartment: builder.query<any, number>({
      query: (dp_id) => ({
        url: `/users/departments/${dp_id}`,
        method: "GET",
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),

    // ── Groups ──────────────────────────────────────────────────────────────
    getGroups: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `/users/groups`,
        method: "GET",
        params,
       
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data?.results ?? [];
      // },
      providesTags: ["Users"],
    }),
    getGroup: builder.query<any, number>({
      query: (id) => ({
        url: `/users/groups/${id}`,
        method: "GET",
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),

    // ── Teams ───────────────────────────────────────────────────────────────
    getTeams: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `/users/teams`,
        method: "GET",
        params,
        
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data?.results ?? [];
      // },
      providesTags: ["Users"],
    }),
    getTeam: builder.query<any, number>({
      query: (id) => ({
        url: `/users/teams/${id}`,
        method: "GET",
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
  }),
});

export const {
  // Users
  useGetTablesQuery,
  useLazyGetTablesQuery,
  useGetUserDetailsQuery,
  useCreateUserMutation,
  useUpdateUserDetailsMutation,
  useDeleteUserMutation,

  // Departments
  useGetDepartmentsQuery,
  useGetDepartmentQuery,

  // Groups
  useGetGroupsQuery,
  useGetGroupQuery,

  // Teams
  useGetTeamsQuery,
  useGetTeamQuery,
} = userApi;