import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Save, 
  Loader2, 
  Package, 
  Building, 
  Calendar, 
  User,
  AlertTriangle,
  CheckCircle,
  XCircle
} from "lucide-react";
import {
  useUpdateGRNMutation,
  useUpdateGRNItemMutation,
  useGetGRNItemsQuery,
  useGetStoresQuery,
  useGetUsersQuery,
} from "@/redux/slices/procurement";
import { GRN, GRNItem } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";
import { inventoryIntegrationHooks } from "@/utils/inventoryIntegration";

interface EditGRNProps {
  open: boolean;
  onClose: () => void;
  grn: GRN | null;
}

const EditGRN = ({ open, onClose, grn }: EditGRNProps) => {
  const [updateGRN, { isLoading: updating }] = useUpdateGRNMutation();
  const [updateGRNItem] = useUpdateGRNItemMutation();
  
  // Fetch supporting data
  const { data: stores } = useGetStoresQuery({});
  const { data: users } = useGetUsersQuery({});
  
  // Fetch GRN items
  const { data: grnItemsData } = useGetGRNItemsQuery({ 
    grn: grn?.id 
  }, { skip: !grn?.id });

  // Form state
  const [formData, setFormData] = useState({
    grn_number: "",
    status: "Partial" as "Partial" | "Full" | "Rejected",
    received_date: "",
    received_by: 0,
    store: 0,
    notes: "",
  });

  const [items, setItems] = useState<GRNItem[]>([]);
  const [originalItems, setOriginalItems] = useState<GRNItem[]>([]);

  // Initialize form data when GRN changes
  useEffect(() => {
    if (grn && open) {
      setFormData({
        grn_number: grn.grn_number,
        status: grn.status,
        received_date: grn.received_date.split('T')[0], // Convert to date format
        received_by: grn.received_by,
        store: grn.store,
        notes: grn.notes || "",
      });
      
      // Set items from GRN or fetch from API
      if (grn.items && grn.items.length > 0) {
        setItems(grn.items);
        setOriginalItems([...grn.items]); // Store original for inventory comparison
      } else if (grnItemsData?.results) {
        setItems(grnItemsData.results);
        setOriginalItems([...grnItemsData.results]);
      }
    }
  }, [grn, open, grnItemsData]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleItemChange = (index: number, field: keyof GRNItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Calculate total price if quantity or unit price changes
    if (field === "quantity_received" || field === "unit_price") {
      const quantity = parseFloat(field === "quantity_received" ? value : updatedItems[index].quantity_received);
      const unitPrice = parseFloat(field === "unit_price" ? value : updatedItems[index].unit_price);
      
      if (!isNaN(quantity) && !isNaN(unitPrice)) {
        updatedItems[index].total_price = (quantity * unitPrice).toFixed(2);
      }
    }
    
    setItems(updatedItems);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!grn) return;

    // Validation
    if (!formData.grn_number.trim()) {
      toast.error("GRN number is required");
      return;
    }

    if (!formData.received_by) {
      toast.error("Please select who received the goods");
      return;
    }

    if (!formData.store) {
      toast.error("Please select a store");
      return;
    }

    try {
      // Update GRN
      const grnPayload = {
        id: grn.id!,
        grn_number: formData.grn_number.trim(),
        status: formData.status,
        received_date: new Date(formData.received_date).toISOString(),
        received_by: formData.received_by,
        store: formData.store,
        notes: formData.notes.trim() || undefined,
      };

      await updateGRN(grnPayload).unwrap();

      // Update GRN items
      const itemPromises = items.map(item => {
        if (item.id) {
          const itemPayload = {
            id: item.id,
            quantity_received: item.quantity_received,
            unit_price: item.unit_price,
            total_price: item.total_price,
            tax_rate: item.tax_rate,
          };
          return updateGRNItem(itemPayload).unwrap();
        }
        return Promise.resolve();
      });

      await Promise.all(itemPromises);

      // Update inventory if quantities changed
      try {
        await inventoryIntegrationHooks.onGRNUpdated(
          originalItems,
          items,
          formData.store,
          grn.id!,
          formData.received_date
        );
      } catch (inventoryError) {
        console.warn("Inventory update failed:", inventoryError);
        // Don't fail the entire operation if inventory update fails
      }

      toast.success("GRN updated successfully");
      onClose();
    } catch (error: any) {
      console.error("Error updating GRN:", error);
      toast.error(error?.data?.message || "Failed to update GRN");
    }
  };

  const getItemStatus = (item: GRNItem) => {
    const received = parseFloat(item.quantity_received.toString());
    if (received === 0) return "Pending";
    return item.status || "Received";
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Received":
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="h-3 w-3 mr-1" />Received</Badge>;
      case "Partial":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><AlertTriangle className="h-3 w-3 mr-1" />Partial</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><AlertTriangle className="h-3 w-3 mr-1" />Pending</Badge>;
      case "Damaged":
        return <Badge className="bg-red-100 text-red-800 border-red-200"><XCircle className="h-3 w-3 mr-1" />Damaged</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };

  if (!grn) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Edit GRN - {grn.grn_number}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="grn_number">GRN Number *</Label>
                <Input
                  id="grn_number"
                  value={formData.grn_number}
                  onChange={(e) => handleInputChange("grn_number", e.target.value)}
                  placeholder="Enter GRN number"
                  required
                />
              </div>

              <div>
                <Label htmlFor="received_date">Received Date *</Label>
                <Input
                  id="received_date"
                  type="date"
                  value={formData.received_date}
                  onChange={(e) => handleInputChange("received_date", e.target.value)}
                  required
                />
              </div>

              <div>
                <Label>Purchase Order</Label>
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                  <Package className="h-4 w-4 text-gray-600" />
                  <span className="font-medium">
                    {grn.purchase_order_number || `PO-${grn.purchase_order}`}
                  </span>
                </div>
              </div>

              <div>
                <Label htmlFor="received_by">Received By *</Label>
                <Select value={formData.received_by.toString()} onValueChange={(value) => handleInputChange("received_by", parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select receiver" />
                  </SelectTrigger>
                  <SelectContent>
                    {users?.results?.map((user: any) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {user.first_name} {user.last_name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="store">Store *</Label>
                <Select value={formData.store.toString()} onValueChange={(value) => handleInputChange("store", parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select store" />
                  </SelectTrigger>
                  <SelectContent>
                    {stores?.results?.map((store: any) => (
                      <SelectItem key={store.id} value={store.id.toString()}>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          {store.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value: any) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Partial">Partial</SelectItem>
                    <SelectItem value="Full">Full</SelectItem>
                    <SelectItem value="Rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Items Section */}
          {items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Received Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Received Qty *</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total Price</TableHead>
                        <TableHead>Tax Rate</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {items.map((item, index) => (
                        <TableRow key={item.id || index}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{item.product_name || `Product ${item.product}`}</p>
                              {item.product_code && (
                                <p className="text-sm text-gray-600">{item.product_code}</p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              value={item.quantity_received}
                              onChange={(e) => handleItemChange(index, "quantity_received", e.target.value)}
                              className="w-24"
                              placeholder="0"
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              value={item.unit_price}
                              onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                              className="w-24"
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            ${item.total_price || "0.00"}
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={item.tax_rate || 0}
                              onChange={(e) => handleItemChange(index, "tax_rate", parseInt(e.target.value) || 0)}
                              className="w-20"
                            />
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(getItemStatus(item))}
                          </TableCell>
                          <TableCell>
                            <Input
                              placeholder="Notes..."
                              value={item.notes || ""}
                              onChange={(e) => handleItemChange(index, "notes", e.target.value)}
                              className="w-32"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Add any additional notes..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={updating}>
              {updating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Update GRN
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditGRN;
