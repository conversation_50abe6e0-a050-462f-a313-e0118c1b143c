import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Plus, 
  Loader2, 
  FileText, 
  Building, 
  Calendar, 
  User,
  AlertTriangle,
  CheckCircle,
  Upload,
  X,
  Receipt,
  DollarSign
} from "lucide-react";
import {
  useCreateInvoiceMutation,
  useCreateInvoiceItemMutation,
  useGetGRNsQ<PERSON>y,
  useGetGRNQuery,
  useGetSuppliersQuery,
  useGetGLAccountsQuery,
  useUploadInvoiceFileMutation,
} from "@/redux/slices/procurement";
import { InvoiceFormData, InvoiceItemFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddInvoiceProps {
  open: boolean;
  onClose: () => void;
}

const AddInvoice = ({ open, onClose }: AddInvoiceProps) => {
  // Mock mutation states
  const [creating, setCreating] = useState(false);
  const [uploading, setUploading] = useState(false);

  const createInvoice = async (payload: any) => {
    setCreating(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setCreating(false);
    return { unwrap: () => Promise.resolve({ id: Date.now() }) };
  };

  const createInvoiceItem = async (payload: any) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    return { unwrap: () => Promise.resolve({ id: Date.now() }) };
  };

  const uploadFile = async (formData: FormData) => {
    setUploading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setUploading(false);
    return { unwrap: () => Promise.resolve({ file_path: "invoices/mock-file.pdf" }) };
  };
  
  // Mock supporting data
  const grnsData = {
    results: [
      {
        id: 1,
        grn_number: "GRN-2025-001",
        supplier_name: "ABC Suppliers Ltd",
        status: "Full",
        items: [
          {
            id: 1,
            product: 1,
            product_name: "Office Paper",
            product_code: "OFF-001",
            quantity_received: "100",
            unit_price: "15.00",
          },
          {
            id: 2,
            product: 2,
            product_name: "Printer Ink",
            product_code: "OFF-002",
            quantity_received: "50",
            unit_price: "25.00",
          },
        ]
      },
      {
        id: 2,
        grn_number: "GRN-2025-002",
        supplier_name: "XYZ Trading Co",
        status: "Full",
        items: [
          {
            id: 3,
            product: 3,
            product_name: "Laptop Computer",
            product_code: "TECH-001",
            quantity_received: "5",
            unit_price: "8000.00",
          },
        ]
      },
    ]
  };

  const suppliers = {
    results: [
      { id: 1, name: "ABC Suppliers Ltd" },
      { id: 2, name: "XYZ Trading Co" },
      { id: 3, name: "Tech Solutions Inc" },
      { id: 4, name: "Global Imports" },
    ]
  };

  const glAccounts = {
    results: [
      { id: 1, account_code: "5001", account_name: "Food Supplies", account_type: "Expense" },
      { id: 2, account_code: "5002", account_name: "Office Supplies", account_type: "Expense" },
      { id: 3, account_code: "5003", account_name: "Equipment", account_type: "Asset" },
      { id: 4, account_code: "5004", account_name: "Raw Materials", account_type: "Expense" },
    ]
  };

  // Selected GRN data
  const [selectedGRNId, setSelectedGRNId] = useState<number | null>(null);
  const selectedGRN = grnsData.results.find(grn => grn.id === selectedGRNId);

  // Form state
  const [formData, setFormData] = useState<InvoiceFormData>({
    invoice_number: "",
    invoice_date: new Date().toISOString().split('T')[0],
    supplier: 0,
    grn: 0,
    status: "Draft",
    total_amount: "0.00",
    currency: "USD",
    gl_account: 0,
    invoice_file: "",
    notes: "",
    items: [],
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Generate invoice number and handle URL params
  useEffect(() => {
    if (open) {
      const invoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;
      setFormData(prev => ({ ...prev, invoice_number: invoiceNumber }));

      // Check if there's a GRN ID in URL params
      const urlParams = new URLSearchParams(window.location.search);
      const grnId = urlParams.get('grn');
      if (grnId) {
        const id = parseInt(grnId);
        setSelectedGRNId(id);
        setFormData(prev => ({ ...prev, grn: id }));
      }
    }
  }, [open]);

  // Update form when GRN is selected
  useEffect(() => {
    if (selectedGRN && selectedGRN.items) {
      const invoiceItems: InvoiceItemFormData[] = selectedGRN.items.map(item => ({
        grn_item: item.id!,
        product: item.product,
        quantity_invoiced: item.quantity_received.toString(),
        unit_price: item.unit_price.toString(),
        total_price: (parseFloat(item.quantity_received.toString()) * parseFloat(item.unit_price.toString())).toFixed(2),
        variance_quantity: "0.00",
        variance_amount: "0.00",
        notes: "",
      }));
      
      const totalAmount = invoiceItems.reduce((sum, item) => 
        sum + parseFloat(item.total_price || "0"), 0
      ).toFixed(2);
      
      setFormData(prev => ({ 
        ...prev, 
        items: invoiceItems,
        grn: selectedGRN.id!,
        supplier: selectedGRN.supplier || 0,
        total_amount: totalAmount,
      }));
    }
  }, [selectedGRN]);

  const handleInputChange = (field: keyof InvoiceFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleGRNSelect = (grnId: string) => {
    const id = parseInt(grnId);
    setSelectedGRNId(id);
    handleInputChange("grn", id);
  };

  const handleItemChange = (index: number, field: keyof InvoiceItemFormData, value: any) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Calculate total price if quantity or unit price changes
    if (field === "quantity_invoiced" || field === "unit_price") {
      const quantity = parseFloat(field === "quantity_invoiced" ? value : updatedItems[index].quantity_invoiced);
      const unitPrice = parseFloat(field === "unit_price" ? value : updatedItems[index].unit_price);
      
      if (!isNaN(quantity) && !isNaN(unitPrice)) {
        updatedItems[index].total_price = (quantity * unitPrice).toFixed(2);
      }

      // Calculate variance
      const grnItem = selectedGRN?.items?.find(item => item.id === updatedItems[index].grn_item);
      if (grnItem) {
        const grnQty = parseFloat(grnItem.quantity_received.toString());
        const grnPrice = parseFloat(grnItem.unit_price.toString());
        
        updatedItems[index].variance_quantity = (quantity - grnQty).toFixed(2);
        updatedItems[index].variance_amount = ((quantity * unitPrice) - (grnQty * grnPrice)).toFixed(2);
      }
    }
    
    // Recalculate total amount
    const totalAmount = updatedItems.reduce((sum, item) => 
      sum + parseFloat(item.total_price || "0"), 0
    ).toFixed(2);
    
    setFormData(prev => ({ 
      ...prev, 
      items: updatedItems,
      total_amount: totalAmount
    }));
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error("File size must be less than 10MB");
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.invoice_number.trim()) {
      toast.error("Invoice number is required");
      return;
    }

    if (!formData.grn) {
      toast.error("Please select a GRN");
      return;
    }

    if (!formData.supplier) {
      toast.error("Please select a supplier");
      return;
    }

    if (!formData.gl_account) {
      toast.error("Please select a GL account");
      return;
    }

    if (formData.items.length === 0) {
      toast.error("Please add at least one item");
      return;
    }

    try {
      // Upload file if selected
      let filePath = "";
      if (selectedFile) {
        const fileFormData = new FormData();
        fileFormData.append("file", selectedFile);

        const uploadResult = await uploadFile(fileFormData);
        const result = await uploadResult.unwrap();
        filePath = result.file_path;
      }

      // Create invoice
      const invoicePayload = {
        invoice_number: formData.invoice_number.trim(),
        invoice_date: formData.invoice_date,
        supplier: formData.supplier,
        grn: formData.grn,
        status: formData.status,
        total_amount: formData.total_amount,
        currency: formData.currency,
        gl_account: formData.gl_account,
        invoice_file: filePath || undefined,
        notes: formData.notes.trim() || undefined,
      };

      const invoiceResult = await createInvoice(invoicePayload);
      const result = await invoiceResult.unwrap();

      // Create invoice items
      if (result.id && formData.items.length > 0) {
        const itemPromises = formData.items.map(async (item) => {
          const itemPayload = {
            invoice: result.id,
            grn_item: item.grn_item,
            product: item.product,
            quantity_invoiced: item.quantity_invoiced,
            unit_price: item.unit_price,
            total_price: item.total_price!,
            variance_quantity: item.variance_quantity!,
            variance_amount: item.variance_amount!,
            notes: item.notes || undefined,
          };
          const itemResult = await createInvoiceItem(itemPayload);
          return itemResult.unwrap();
        });

        await Promise.all(itemPromises);
      }

      toast.success("Invoice created successfully");
      handleClose();
    } catch (error: any) {
      console.error("Error creating invoice:", error);
      toast.error(error?.data?.message || "Failed to create invoice");
    }
  };

  const handleClose = () => {
    setFormData({
      invoice_number: "",
      invoice_date: new Date().toISOString().split('T')[0],
      supplier: 0,
      grn: 0,
      status: "Draft",
      total_amount: "0.00",
      currency: "USD",
      gl_account: 0,
      invoice_file: "",
      notes: "",
      items: [],
    });
    setSelectedGRNId(null);
    setSelectedFile(null);

    // Clear URL params if they exist
    const url = new URL(window.location.href);
    url.searchParams.delete('grn');
    window.history.replaceState({}, '', url.toString());

    onClose();
  };

  // Calculate variance warnings
  const getVarianceWarnings = () => {
    const warnings: string[] = [];
    formData.items.forEach((item, index) => {
      const qtyVariance = parseFloat(item.variance_quantity || "0");
      const amountVariance = parseFloat(item.variance_amount || "0");
      
      if (Math.abs(qtyVariance) > 0.01) {
        warnings.push(`Item ${index + 1}: Quantity variance of ${qtyVariance}`);
      }
      if (Math.abs(amountVariance) > 0.01) {
        warnings.push(`Item ${index + 1}: Amount variance of ${amountVariance}`);
      }
    });
    return warnings;
  };

  const varianceWarnings = getVarianceWarnings();

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Create New Invoice
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="invoice_number">Invoice Number *</Label>
                <Input
                  id="invoice_number"
                  value={formData.invoice_number}
                  onChange={(e) => handleInputChange("invoice_number", e.target.value)}
                  placeholder="Enter invoice number"
                  required
                />
              </div>

              <div>
                <Label htmlFor="invoice_date">Invoice Date *</Label>
                <Input
                  id="invoice_date"
                  type="date"
                  value={formData.invoice_date}
                  onChange={(e) => handleInputChange("invoice_date", e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="grn">GRN *</Label>
                <Select value={formData.grn.toString()} onValueChange={handleGRNSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select GRN" />
                  </SelectTrigger>
                  <SelectContent>
                    {grnsData?.results?.map((grn: any) => (
                      <SelectItem key={grn.id} value={grn.id.toString()}>
                        <div className="flex items-center gap-2">
                          <Receipt className="h-4 w-4" />
                          {grn.grn_number} - {grn.supplier_name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="supplier">Supplier *</Label>
                <Select value={formData.supplier.toString()} onValueChange={(value) => handleInputChange("supplier", parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers?.results?.map((supplier: any) => (
                      <SelectItem key={supplier.id} value={supplier.id.toString()}>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          {supplier.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="gl_account">GL Account *</Label>
                <Select value={formData.gl_account.toString()} onValueChange={(value) => handleInputChange("gl_account", parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select GL account" />
                  </SelectTrigger>
                  <SelectContent>
                    {glAccounts?.results?.map((account: any) => (
                      <SelectItem key={account.id} value={account.id.toString()}>
                        <div>
                          <p className="font-medium">{account.account_code} - {account.account_name}</p>
                          <p className="text-sm text-gray-600">{account.account_type}</p>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select value={formData.currency} onValueChange={(value) => handleInputChange("currency", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="KES">KES</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Invoice File</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="invoice_file">Upload Invoice PDF</Label>
                  <div className="mt-2">
                    <input
                      id="invoice_file"
                      type="file"
                      accept=".pdf"
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById("invoice_file")?.click()}
                      disabled={uploading}
                    >
                      {uploading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Upload className="h-4 w-4 mr-2" />
                      )}
                      {selectedFile ? "Change File" : "Select File"}
                    </Button>
                    {selectedFile && (
                      <div className="flex items-center gap-2 mt-2">
                        <FileText className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">{selectedFile.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedFile(null)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Variance Warnings */}
          {varianceWarnings.length > 0 && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader>
                <CardTitle className="text-lg text-yellow-800 flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Variance Warnings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {varianceWarnings.map((warning, index) => (
                    <li key={index} className="text-sm text-yellow-700">
                      • {warning}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Invoice Items */}
          {formData.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Invoice Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>GRN Qty</TableHead>
                        <TableHead>Invoice Qty *</TableHead>
                        <TableHead>Unit Price *</TableHead>
                        <TableHead>Total Price</TableHead>
                        <TableHead>Variance</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {formData.items.map((item, index) => {
                        const grnItem = selectedGRN?.items?.find(gItem => gItem.id === item.grn_item);
                        const hasVariance = Math.abs(parseFloat(item.variance_quantity || "0")) > 0.01 || 
                                          Math.abs(parseFloat(item.variance_amount || "0")) > 0.01;
                        
                        return (
                          <TableRow key={index}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{grnItem?.product_name || `Product ${item.product}`}</p>
                                {grnItem?.product_code && (
                                  <p className="text-sm text-gray-600">{grnItem.product_code}</p>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {grnItem ? parseFloat(grnItem.quantity_received.toString()).toLocaleString() : "N/A"}
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.quantity_invoiced}
                                onChange={(e) => handleItemChange(index, "quantity_invoiced", e.target.value)}
                                className="w-24"
                                placeholder="0"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.unit_price}
                                onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                                className="w-24"
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              {formData.currency} {item.total_price || "0.00"}
                            </TableCell>
                            <TableCell>
                              {hasVariance ? (
                                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  Variance
                                </Badge>
                              ) : (
                                <Badge className="bg-green-100 text-green-800 border-green-200">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Match
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <Input
                                placeholder="Notes..."
                                value={item.notes || ""}
                                onChange={(e) => handleItemChange(index, "notes", e.target.value)}
                                className="w-32"
                              />
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          {formData.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-600">
                      {formData.items.length}
                    </p>
                    <p className="text-sm text-gray-600">Total Items</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {formData.items.filter(item => 
                        Math.abs(parseFloat(item.variance_quantity || "0")) <= 0.01
                      ).length}
                    </p>
                    <p className="text-sm text-gray-600">Matching Items</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-600">
                      {varianceWarnings.length}
                    </p>
                    <p className="text-sm text-gray-600">Variances</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600">
                      {formData.currency} {parseFloat(formData.total_amount).toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-600">Total Amount</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Add any additional notes..."
              rows={3}
            />
          </div>

          {/* Variance Warnings */}
          {varianceWarnings.length > 0 && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader>
                <CardTitle className="text-lg text-yellow-800 flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Variance Warnings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1">
                  {varianceWarnings.map((warning, index) => (
                    <li key={index} className="text-sm text-yellow-700">
                      • {warning}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Invoice Items */}
          {formData.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Invoice Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>GRN Qty</TableHead>
                        <TableHead>Invoice Qty *</TableHead>
                        <TableHead>Unit Price *</TableHead>
                        <TableHead>Total Price</TableHead>
                        <TableHead>Variance</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {formData.items.map((item, index) => {
                        const grnItem = selectedGRN?.items?.find(gItem => gItem.id === item.grn_item);
                        const hasVariance = Math.abs(parseFloat(item.variance_quantity || "0")) > 0.01 ||
                                          Math.abs(parseFloat(item.variance_amount || "0")) > 0.01;

                        return (
                          <TableRow key={index}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{grnItem?.product_name || `Product ${item.product}`}</p>
                                {grnItem?.product_code && (
                                  <p className="text-sm text-gray-600">{grnItem.product_code}</p>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {grnItem ? parseFloat(grnItem.quantity_received.toString()).toLocaleString() : "N/A"}
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.quantity_invoiced}
                                onChange={(e) => handleItemChange(index, "quantity_invoiced", e.target.value)}
                                className="w-24"
                                placeholder="0"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.unit_price}
                                onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                                className="w-24"
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              {formData.currency} {item.total_price || "0.00"}
                            </TableCell>
                            <TableCell>
                              {hasVariance ? (
                                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  Variance
                                </Badge>
                              ) : (
                                <Badge className="bg-green-100 text-green-800 border-green-200">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Match
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <Input
                                placeholder="Notes..."
                                value={item.notes || ""}
                                onChange={(e) => handleItemChange(index, "notes", e.target.value)}
                                className="w-32"
                              />
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          {formData.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-600">
                      {formData.items.length}
                    </p>
                    <p className="text-sm text-gray-600">Total Items</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {formData.items.filter(item =>
                        Math.abs(parseFloat(item.variance_quantity || "0")) <= 0.01
                      ).length}
                    </p>
                    <p className="text-sm text-gray-600">Matching Items</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-600">
                      {varianceWarnings.length}
                    </p>
                    <p className="text-sm text-gray-600">Variances</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600">
                      {formData.currency} {parseFloat(formData.total_amount).toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-600">Total Amount</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Add any additional notes..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating || uploading}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Invoice
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddInvoice;
