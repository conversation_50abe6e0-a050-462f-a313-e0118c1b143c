import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Screen } from '@/app-components/layout/screen';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X } from 'lucide-react';
import { BranchFormData, TIMEZONES, CURRENCIES } from '@/types/pos';
import {
  useCreateBranchMutation,
  useUpdateBranchMutation,
  useGetBranchQuery
} from '@/redux/slices/branches';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';

interface BranchFormProps {
  mode: 'create' | 'edit';
}

const BranchForm: React.FC<BranchFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();

  // API hooks
  const [createBranch, { isLoading: isCreating }] = useCreateBranchMutation();
  const [updateBranch, { isLoading: isUpdating }] = useUpdateBranchMutation();
  const { data: branchData, isLoading: isLoadingBranch } = useGetBranchQuery(id!, {
    skip: mode === 'create' || !id,
  });

  const form = useForm<BranchFormData>({
    defaultValues: {
      name: '',
      location: '',
      timezone: 'Africa/Nairobi',
      currency: 'KES',
      language: 'en',
      tax_config: {},
      is_active: true,
    },
  });

  // Load branch data for edit mode
  useEffect(() => {
    if (mode === 'edit' && branchData) {
      form.reset({
        name: branchData.name,
        location: branchData.location,
        timezone: branchData.timezone || 'Africa/Nairobi',
        currency: branchData.currency || 'KES',
        language: branchData.language || 'en',
        tax_config: {},
        is_active: branchData.is_active ?? true,
      });
    }
  }, [branchData, form, mode]);

  const onSubmit = async (data: BranchFormData) => {
    try {
      // Validate required fields
      if (!data.name || !data.location) {
        handleApiError({ message: 'Name and location are required fields' }, 'validate form');
        return;
      }

      const generatedCode = generateBranchCode();
      if (!generatedCode) {
        handleApiError({ message: 'Failed to generate branch code. Please ensure name is provided.' }, 'generate branch code');
        return;
      }

      const branchPayload = {
        branch_code: generatedCode,
        name: data.name,
        location: data.location,
        timezone: data.timezone || 'Africa/Nairobi',
        currency: data.currency || 'KES',
        language: data.language || 'en',
        is_active: data.is_active ?? true,
      };

      console.log('Form data:', data);
      console.log('Generated branch code:', generatedCode);
      console.log('Submitting branch payload:', branchPayload);

      if (mode === 'create') {
        const result = await createBranch(branchPayload).unwrap();
        console.log('Branch created successfully:', result);
        handleApiSuccess('Branch created successfully!', result);
      } else if (mode === 'edit' && id) {
        const result = await updateBranch({ id, data: branchPayload }).unwrap();
        console.log('Branch updated successfully:', result);
        handleApiSuccess('Branch updated successfully!', result);
      }

      // Navigate back to branches list
      navigate('/pos/branches');
    } catch (error: any) {
      handleApiError(error, mode === 'create' ? 'create branch' : 'update branch');
    }
  };

  const generateBranchCode = () => {
    const name = form.getValues('name');
    if (name && name.trim().length > 0) {
      // Clean the name and take first 3 characters
      const cleanName = name.trim().replace(/[^a-zA-Z]/g, '');
      const namePrefix = cleanName.length >= 3 ? cleanName.substring(0, 3) : cleanName.padEnd(3, 'X');
      const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      const code = 'BR' + namePrefix.toUpperCase() + randomSuffix;
      return code;
    }
    return '';
  };

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/branches')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add New Branch' : 'Edit Branch'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Create a new branch with localization and tax settings'
              : 'Update branch information and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the basic details for the branch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  rules={{ required: 'Branch name is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter branch name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="space-y-2">
                  <Label>Branch Code</Label>
                  <div className="flex space-x-2">
                    <Input 
                      value={generateBranchCode()} 
                      readOnly 
                      className="bg-muted"
                      placeholder="Auto-generated"
                    />
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => {
                        // Force re-render to generate new code
                        form.trigger('name');
                      }}
                    >
                      Generate
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Code will be auto-generated based on branch name
                  </p>
                </div>
              </div>

              <FormField
                control={form.control}
                name="location"
                rules={{ required: 'Location is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter complete physical address including street, building, and postal code"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Complete physical address of the branch
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="language"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Language</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="sw">Swahili</SelectItem>
                        <SelectItem value="fr">French</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Default language for this branch
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Localization Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Localization Settings</CardTitle>
              <CardDescription>
                Configure timezone and currency for this branch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="timezone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Timezone</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select timezone" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {TIMEZONES.map((timezone) => (
                            <SelectItem key={timezone} value={timezone}>
                              {timezone}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {CURRENCIES.map((currency) => (
                            <SelectItem key={currency.code} value={currency.code}>
                              {currency.symbol} {currency.name} ({currency.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Status */}
          <Card>
            <CardHeader>
              <CardTitle>Branch Status</CardTitle>
              <CardDescription>
                Set the operational status of this branch
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Active Branch
                      </FormLabel>
                      <FormDescription>
                        Enable this branch for operations and transactions
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/pos/branches')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating || isUpdating || isLoadingBranch}>
              <Save className="h-4 w-4 mr-2" />
              {(isCreating || isUpdating) ? 'Saving...' : mode === 'create' ? 'Create Branch' : 'Update Branch'}
            </Button>
          </div>
        </form>
      </Form>
      </div>
    </Screen>
  );
};

export default BranchForm;
