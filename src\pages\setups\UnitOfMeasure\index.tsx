import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { UoMTypes } from "@/types/UoM";
import UoMTestData from "./UoMTestData";
import { searchDebouncer } from "@/utils/debouncers";
import AddUoM from "./modals/AddUoM";
import UoMDetails from "./modals/UoMDetails";
import { useGetUnitOfMeasureQuery } from "@/redux/slices/uom";

const index = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUoM, setSelectedUoM] = useState<UoMTypes | null>(null);

  const {
    data: UoMs,
    isLoading,
    isFetching,
    isError,
    error,
    refetch,
  } = useGetUnitOfMeasureQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
  });

  const columns: ColumnDef<UoMTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      enableColumnFilter: false,
    },
    {
      accessorKey: "symbol",
      header: "Symbol",
      enableColumnFilter: false,
    },
    {
      accessorKey: "conversion_base",
      header: "Conversion Base",
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewDetails(row.original)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row.original)}
          >
            Edit
          </Button>
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  const handleViewDetails = (UoM: UoMTypes) => {
    setSelectedUoM(UoM);
    setIsDetailModalOpen(true);
  };

  const handleEdit = (UoM: UoMTypes) => {
    setSelectedUoM(UoM);
    setIsEditModalOpen(true);
  };

  return (
    <Screen>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold">Units of Measure</h1>
        <div className="flex items-center gap-2">
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            Add unit of measure
          </Button>
        </div>
      </div>

      <DataTable<UoMTypes>
        data={UoMs?.data?.results || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-gray-50"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search suppliers..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={UoMs?.data?.total_data || 0}
      />

      {/* Modal Components */}
      {isAddModalOpen && (
        <AddUoM
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          refetch={refetch}
        />
      )}

      {isEditModalOpen && (
        <AddUoM
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          updateData={selectedUoM!}
          refetch={refetch}
        />
      )}

      {isDetailModalOpen && (
        <UoMDetails
          isOpen={isDetailModalOpen}
          onClose={() => setIsDetailModalOpen(false)}
          uom={selectedUoM!}
        />
      )}
    </Screen>
  );
};

export default index;
