import { apiSlice } from "../apiSlice";

export const recipeApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getRecipe: builder.query({
      query: (params) => ({
        url: "/menu/recipes",
        method: "GET",
        params,
        
      }),
    }),

    retrieveRecipesMenus: builder.query({
      query: (id) => ({
        url: `/menu/recipes${id}`,
        method: "GET",
      }),
    }),

    addrecipes: builder.mutation({
      query: (payload) => ({
        url: "/menu/recipes", // Removed ID from URL, as POST typically creates a new resource
        method: "POST",
        body: payload,
      }),
    }),

    deleterecipes: builder.mutation({
      query: (id) => ({
        url: `/menu/recipes/${id}`,
        method: "DELETE",
      }),
    }),

    patchrecipes: builder.mutation({
      query: (payload) => ({
        url: `/menu/recipes/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
    useGetRecipeQuery,
    useLazyGetRecipeQuery,
    usePatchrecipesMutation,
    useAddrecipesMutation,
    useDeleterecipesMutation,
    
} = recipeApiSlice;
