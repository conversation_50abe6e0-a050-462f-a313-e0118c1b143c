import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CheckInForm } from "./types/types";


interface CheckInDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  checkInForm: CheckInForm;
  setCheckInForm: React.Dispatch<React.SetStateAction<CheckInForm>>;
  onCheckIn: () => void;
}

export const CheckInDialog: React.FC<CheckInDialogProps> = ({
  open,
  onOpenChange,
  checkInForm,
  setCheckInForm,
  onCheckIn,
}) => (
  <Dialog open={open} onOpenChange={onO<PERSON><PERSON><PERSON><PERSON>}>
    <DialogContent className="sm:max-w-md bg-white/95 backdrop-blur-sm border-0">
      <DialogHeader>
        <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          New Guest Check-in
        </DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <Label htmlFor="tableNumber">Table Number</Label>
          <Select
            value={checkInForm.tableNumber}
            onValueChange={(value) => setCheckInForm({ ...checkInForm, tableNumber: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select table" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 20 }, (_, i) => i + 1).map((num) => (
                <SelectItem key={num} value={`T-${String(num).padStart(2, "0")}`}>
                  Table {num}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="guestCount">Number of Guests</Label>
          <Input
            id="guestCount"
            type="number"
            placeholder="Enter guest count"
            value={checkInForm.guestCount}
            onChange={(e) => setCheckInForm({ ...checkInForm, guestCount: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="waiterName">Waiter Name</Label>
          <Select
            value={checkInForm.waiterName}
            onValueChange={(value) => setCheckInForm({ ...checkInForm, waiterName: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select waiter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Sarah Johnson">Sarah Johnson</SelectItem>
              <SelectItem value="Mike Chen">Mike Chen</SelectItem>
              <SelectItem value="Emma Wilson">Emma Wilson</SelectItem>
              <SelectItem value="James Rodriguez">James Rodriguez</SelectItem>
              <SelectItem value="Lisa Thompson">Lisa Thompson</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="specialRequests">Special Requests (Optional)</Label>
          <Input
            id="specialRequests"
            placeholder="Any special requests or notes"
            value={checkInForm.specialRequests}
            onChange={(e) => setCheckInForm({ ...checkInForm, specialRequests: e.target.value })}
          />
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" onClick={() => onOpenChange(false)} className="hover:bg-gray-50">
          Cancel
        </Button>
        <Button
          onClick={onCheckIn}
          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          disabled={!checkInForm.tableNumber || !checkInForm.guestCount || !checkInForm.waiterName}
        >
          Check In
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
);