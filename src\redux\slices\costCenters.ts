import { apiSlice } from "../apiSlice";

// Types for Cost Center API
export interface CostCenter {
  id?: number;
  name: string;
  code: string;
  description: string;
  budget_limit?: string;
  budget_frequency?: 'MONTHLY' | 'ANNUAL';
  is_active?: boolean;
  default_store?: string;
}

export const costCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all cost centers
    getCostCenters: builder.query<CostCenter[], any>({
      query: (params) => ({
        url: "/setup/cost-centers",
        method: "GET",
        params: params,
      }),
      providesTags: ["CostCenters"],
    }),

    // Get single cost center
    getCostCenter: builder.query<CostCenter, string>({
      query: (id) => ({
        url: `/setup/cost-centers/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "CostCenters", id }],
    }),

    // Create cost center
    createCostCenter: builder.mutation<CostCenter, Partial<CostCenter>>({
      query: (payload) => ({
        url: "/setup/cost-centers",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["CostCenters"],
    }),

    // Update cost center
    updateCostCenter: builder.mutation<CostCenter, { id: string; data: Partial<CostCenter> }>({
      query: ({ id, data }) => ({
        url: `/setup/cost-centers/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "CostCenters", id }, "CostCenters"],
    }),

    // Delete cost center
    deleteCostCenter: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/cost-centers/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["CostCenters"],
    }),
  }),
});

export const { 
  useGetCostCentersQuery, 
  useGetCostCenterQuery,
  useCreateCostCenterMutation,
  useUpdateCostCenterMutation,
  useDeleteCostCenterMutation,
} = costCenterApiSlice;
