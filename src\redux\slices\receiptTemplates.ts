import { apiSlice } from "../apiSlice";

// Types for Receipt Template API
export interface ReceiptTemplate {
  id?: number;
  name: string;
  logo?: string;
  foot_text?: string;
  header_text?: string;
  language: string;
  layout_config?: any;
}

export const receiptTemplateApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all receipt templates
    getReceiptTemplates: builder.query<ReceiptTemplate[], any>({
      query: (params) => ({
        url: "/setup/receipt-templates",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        // Handle different response structures
        if (Array.isArray(response)) {
          return response;
        } else if (response && response.data) {
          if (Array.isArray(response.data)) {
            return response.data;
          } else if (response.data.results && Array.isArray(response.data.results)) {
            return response.data.results;
          } else if (response.data.receipt_templates && Array.isArray(response.data.receipt_templates)) {
            return response.data.receipt_templates;
          }
        }
        // Fallback to empty array if structure is unexpected
        console.warn("Unexpected receipt templates API response structure:", response);
        return [];
      },
      providesTags: ["ReceiptTemplates"],
    }),

    // Get single receipt template
    getReceiptTemplate: builder.query<ReceiptTemplate, string>({
      query: (id) => ({
        url: `/setup/receipt-templates/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "ReceiptTemplates", id }],
    }),

    // Create receipt template
    createReceiptTemplate: builder.mutation<ReceiptTemplate, Partial<ReceiptTemplate>>({
      query: (payload) => ({
        url: "/setup/receipt-templates",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["ReceiptTemplates"],
    }),

    // Update receipt template
    updateReceiptTemplate: builder.mutation<ReceiptTemplate, { id: string; data: Partial<ReceiptTemplate> }>({
      query: ({ id, data }) => ({
        url: `/setup/receipt-templates/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "ReceiptTemplates", id }, "ReceiptTemplates"],
    }),

    // Delete receipt template
    deleteReceiptTemplate: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/receipt-templates/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["ReceiptTemplates"],
    }),
  }),
});

export const { 
  useGetReceiptTemplatesQuery, 
  useGetReceiptTemplateQuery,
  useCreateReceiptTemplateMutation,
  useUpdateReceiptTemplateMutation,
  useDeleteReceiptTemplateMutation,
} = receiptTemplateApiSlice;
