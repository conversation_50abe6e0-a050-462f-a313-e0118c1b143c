import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Building, FileText, Upload, DollarSign } from "lucide-react";
import {
  useCreateRFQResponseMutation,
  useCreateRFQResponseItemMutation,
  useGetRFQQuery,
  useGetSuppliersQuery,
  useGetCurrenciesQuery,
} from "@/redux/slices/procurement";
import { RFQResponseFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddRFQResponseProps {
  open: boolean;
  onClose: () => void;
  rfqId: number;
}

const AddRFQResponse = ({ open, onClose, rfqId }: AddRFQResponseProps) => {
  const [createRFQResponse, { isLoading: creating }] = useCreateRFQResponseMutation();
  const [createRFQResponseItem] = useCreateRFQResponseItemMutation();
  
  // Fetch supporting data
  const { data: rfq } = useGetRFQQuery(rfqId);
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: currencies } = useGetCurrenciesQuery({});

  const [formData, setFormData] = useState<RFQResponseFormData>({
    submitted_at: new Date().toISOString(),
    submitted_by: "",
    rfq: rfqId,
    supplier: 0,
    notes: "",
    items: [],
  });

  const handleInputChange = (field: keyof RFQResponseFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };



  const calculateTotalValue = () => {
    return formData.items.reduce((total, item) => {
      const quantity = rfq?.items?.find(rfqItem => rfqItem.product === item.product)?.quantity || 0;
      const unitPrice = typeof item.unit_price === 'string' ? parseFloat(item.unit_price) || 0 : item.unit_price;
      return total + (quantity * unitPrice);
    }, 0);
  };

  const resetForm = () => {
    setFormData({
      submitted_at: new Date().toISOString(),
      submitted_by: "",
      rfq: rfqId,
      supplier: 0,
      notes: "",
      items: rfq?.items?.map(item => ({
        unit_price: "",
        quantity: item.quantity || "",
        delivery_time_days: undefined,
        currency: "KES",
        total_price: "",
        product: item.product || 0,
        unit_of_measure: item.unit_of_measure || 0,
        tax_rate: undefined,
      })) || [],
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation - Check all required fields per API specification
    if (!formData.submitted_by.trim()) {
      toast.error("Please enter who is submitting this response");
      return;
    }

    if (!formData.supplier) {
      toast.error("Please select a supplier");
      return;
    }

    const validItems = formData.items.filter(
      (item) => item.unit_price && item.quantity && item.currency && item.product && item.unit_of_measure
    );

    if (validItems.length === 0) {
      toast.error("Please provide complete information for at least one item");
      return;
    }

    try {
      const payload = {
        submitted_at: formData.submitted_at,
        submitted_by: formData.submitted_by.trim(),
        rfq: formData.rfq,
        supplier: Number(formData.supplier),
        notes: formData.notes || null,
      };

      const result = await createRFQResponse(payload).unwrap();

      // Create items separately if the response was created successfully
      if (result.id && validItems.length > 0) {
        for (const item of validItems) {
          await createRFQResponseItem({
            unit_price: item.unit_price,
            quantity: item.quantity,
            delivery_time_days: item.delivery_time_days || null,
            currency: item.currency,
            total_price: item.total_price || null,
            response: result.id,
            product: Number(item.product),
            unit_of_measure: Number(item.unit_of_measure),
            tax_rate: item.tax_rate || null,
          }).unwrap();
        }
      }

      toast.success("RFQ response created successfully");
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create RFQ response");
    }
  };

  useEffect(() => {
    if (open && rfq) {
      setFormData(prev => ({
        ...prev,
        items: rfq.items?.map(item => ({
          unit_price: "",
          quantity: item.quantity || "",
          delivery_time_days: undefined,
          currency: "KES",
          total_price: "",
          product: item.product || 0,
          unit_of_measure: item.unit_of_measure || 0,
          tax_rate: undefined,
        })) || [],
      }));
    }
  }, [open, rfq]);

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Add RFQ Response - {rfq?.rfq_number}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building className="h-4 w-4" />
                Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="supplier">Supplier *</Label>
                  <Select
                    value={formData.supplier ? formData.supplier.toString() : ""}
                    onValueChange={(value) => handleInputChange("supplier", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers?.data?.results?.map((supplier: any) => (
                        <SelectItem key={supplier.id} value={supplier.id.toString()}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="submitted_by">Submitted By *</Label>
                  <Input
                    id="submitted_by"
                    value={formData.submitted_by}
                    onChange={(e) => handleInputChange("submitted_by", e.target.value)}
                    placeholder="Enter name of person submitting (max 100 characters)"
                    maxLength={100}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Name of the person submitting this response (required)
                  </p>
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes or comments about this response..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Optional notes about this RFQ response
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Items Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <DollarSign className="h-4 w-4" />
                Item Pricing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {rfq?.items?.map((rfqItem, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{rfqItem.product_name}</h4>
                      <p className="text-sm text-gray-600">
                        Quantity: {rfqItem.quantity} {rfqItem.unit_of_measure_name}
                      </p>
                      {rfqItem.specifications && (
                        <p className="text-sm text-gray-500 mt-1">
                          Specs: {rfqItem.specifications}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <Label>Unit Price *</Label>
                      <Input
                        placeholder="Enter unit price (decimal as string)"
                        value={formData.items[index]?.unit_price || ""}
                        onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Unit price as decimal string (required)
                      </p>
                    </div>

                    <div>
                      <Label>Quantity *</Label>
                      <Input
                        placeholder="Enter quantity (decimal as string)"
                        value={formData.items[index]?.quantity || ""}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Quantity as decimal string (required)
                      </p>
                    </div>

                    <div>
                      <Label>Currency *</Label>
                      <Select
                        value={formData.items[index]?.currency || "KES"}
                        onValueChange={(value) => handleItemChange(index, "currency", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                          <SelectItem value="USD">USD - US Dollar</SelectItem>
                          <SelectItem value="EUR">EUR - Euro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Delivery Time (Days)</Label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="Days"
                        value={formData.items[index]?.delivery_time_days || ""}
                        onChange={(e) => handleItemChange(index, "delivery_time_days", parseInt(e.target.value) || undefined)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Total Price</Label>
                      <Input
                        placeholder="Enter total price (optional)"
                        value={formData.items[index]?.total_price || ""}
                        onChange={(e) => handleItemChange(index, "total_price", e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Total price for this item (optional)
                      </p>
                    </div>

                    <div>
                      <Label>Tax Rate (%)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        placeholder="0"
                        value={formData.items[index]?.tax_rate || ""}
                        onChange={(e) => handleItemChange(index, "tax_rate", parseInt(e.target.value) || undefined)}
                      />
                    </div>
                  </div>
                </div>
              ))}

              {/* Total Summary */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-bold">
                  <span>Total Response Value:</span>
                  <span>KES {calculateTotalValue().toLocaleString()}</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Calculated from individual item prices and quantities
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Response...
                </>
              ) : (
                "Submit Response"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddRFQResponse;
