import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { searchDebouncer } from "@/utils/debouncers";
import { Link } from "react-router-dom";
import { mainCategoryTestData } from "./ProductCategoryTestData";
import AddProductMainCategory from "./modals/AddProductMainCategory";
import { ProductMainCategory } from "@/types/products";
import { useGetProductMainCategoryQuery } from "@/redux/slices/productCategories";

const ProductMainCategoryPage = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMainCategory, setselectedMainCategory] =
    useState<ProductMainCategory | null>(null);

  const {
    data: productMainCategories,
    isLoading,
    isFetching,
    isError,
    error,
  } = useGetProductMainCategoryQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
  });

  const columns: ColumnDef<ProductMainCategory>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <Link to={`/product-categories/${row.original.id}`}>
          <span className="font-medium underline capitalize text-blue-400">
            {row.original.name}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "code",
      header: "Code",
      enableColumnFilter: false,
    },
    {
      accessorKey: "description",
      header: "Description",
      enableColumnFilter: false,
    },
    {
      accessorKey: "cost_center",
      header: "Cost Center",
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Link to={`/product-categories/${row.original.id}`}>
            <Button variant="outline" size="sm">
              View
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row.original)}
          >
            Edit
          </Button>
          {/* <Button
            variant="ghost"
            size="icon"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button> */}
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  const handleEdit = (store: ProductMainCategory) => {
    setselectedMainCategory(store);
    setIsEditModalOpen(true);
  };

  return (
    <Screen>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold">Products Main Categories</h1>
        <div className="flex items-center gap-2">
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            Add Main Category
          </Button>
        </div>
      </div>

      <DataTable<ProductMainCategory>
        data={productMainCategories?.data?.results || []}
        // data={mainCategoryTestData || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-gray-50"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search suppliers..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={productMainCategories?.data?.total_data || 0}
        // totalItems={mainCategoryTestData.length || 0}
      />

      {/* Modal Components */}
      {isAddModalOpen && (
        <AddProductMainCategory
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}

      {isEditModalOpen && (
        <AddProductMainCategory
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          updateData={selectedMainCategory!}
        />
      )}
    </Screen>
  );
};

export default ProductMainCategoryPage;
