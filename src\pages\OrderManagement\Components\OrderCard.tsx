import React, { useState } from 'react';
import OrderTrackerPipeline from './OrderPipeline';


interface OrderCardProps {
  orderNumber: string;
  status: string;
  date: string;
  total: string;
  tableNumber?: string; // Optional, to match OrderTrackerPipeline
  guestCount?: string; // Optional, to match Order
}

function OrderCard({ orderNumber, status, date, total, tableNumber, guestCount }: OrderCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
 

  const handleClick = () => {
    setIsModalOpen(true); // Open the modal on click
  };

  return (
    <>
      <div 
        onClick={handleClick}
        className="bg-white rounded-lg shadow-md hover:shadow-lg border border-outline hover:border-primary w-80 h-[25rem] flex flex-col justify-center items-center cursor-pointer transition-all duration-200 hover:scale-105 active:scale-95 group"
      >
        <div className="text-center p-6">
          <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mb-4 group-hover:bg-secondary/20 transition-colors">
            <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Order #{orderNumber}</h3>
          <p className="text-sm text-gray-600 mb-4">Click to view details</p>
          
          <div className="space-y-2 text-sm text-gray-500">
            <p>Status: <span className="text-green-600 font-medium">{status}</span></p>
            <p>Date: {date}</p>
            <p>Total: {total}</p>
          </div>
        </div>
        
        <div className="mt-auto mb-6">
          <div className="px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium group-hover:bg-primary/20 transition-colors">
            View Order →
          </div>
        </div>
      </div>

      {/* Render the OrderTrackerPipeline modal */}
      <OrderTrackerPipeline
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        order={{ orderNumber, status, date, total, tableNumber, guestCount }}
      />
    </>
  );
}

export default OrderCard;