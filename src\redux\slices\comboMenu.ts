import { apiSlice } from "../apiSlice";

export const comboCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getComboMenu: builder.query({
      query: (params) => ({
        url: "/menu/combo-components",
        method: "GET",
        params,
        
      }),
    }),

    retrieveComboMenus: builder.query({
      query: (id) => ({
        url: `/menu/combo-components${id}`,
        method: "GET",
      }),
    }),

    addComboMenu: builder.mutation({
      query: (payload) => ({
        url: "/menu/combo-meals", // Removed ID from URL, as POST typically creates a new resource
        method: "POST",
        body: payload,
      }),
    }),

    deleteMenus: builder.mutation({
      query: (id) => ({
        url: `/menu/combo-components/${id}`,
        method: "DELETE",
      }),
    }),

    patchMenus: builder.mutation({
      query: (payload) => ({
        url: `/menu/combo-components/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
    useGetComboMenuQuery,
    useLazyGetComboMenuQuery,
    usePatchMenusMutation,
    useAddComboMenuMutation,
    useDeleteMenusMutation,
} = comboCenterApiSlice;
