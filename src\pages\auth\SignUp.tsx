import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>O<PERSON>, Loader } from 'lucide-react';
import Logo from '@/assets/logo.png';
import { useForm } from 'react-hook-form';
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod";
import { Link, useNavigate } from 'react-router-dom';
import { usePostRegistrationMutation } from '@/redux/slices/auth';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetUserRolesQuery } from '@/redux/slices/userRoles';
import { toast } from '@/components/custom/Toast/MyToast';

const SignupPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  // zod schema for signup validation - Updated for API specification
  const formSchema = z.object({
    email: z.string().email({ message: "Invalid email address." }),
    password: z.string().min(6, { message: "Password is required (min 6 chars)." }),
    confirmPassword: z.string().min(6, { message: "Please confirm your password." }),
    username: z.string().min(3, { message: "Username is required (min 3 chars)." }),
    first_name: z.string().min(2, { message: "First name is required." }),
    last_name: z.string().min(2, { message: "Last name is required." }),
    employee_no: z.string().min(1, { message: "Employee number is required." }),
    branch: z.string().min(1, { message: "Branch selection is required." }),
    role: z.string().optional().transform((val) => val ? parseInt(val, 10) : 1),
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const { register, handleSubmit, formState: { errors } } = form;

  const navigate = useNavigate();
  const [postRegistration, { isLoading: isSignUpLoading }] = usePostRegistrationMutation();
  const { data: branches = [], isLoading: loadingBranches } = useGetBranchesQuery({});
  const { data: userRoles = [], isLoading: loadingUserRoles, error: userRolesError } = useGetUserRolesQuery({});



  // Auto-select first role if available and no role is currently selected
  useEffect(() => {
    if (!loadingUserRoles && Array.isArray(userRoles) && userRoles.length > 0) {
      const currentRole = form.getValues('role');
      if (!currentRole) {
        const firstRole = userRoles[0];
        if (firstRole?.id) {
          form.setValue('role', firstRole.id?.toString() || '1');

        }
      }
    }
  }, [userRoles, loadingUserRoles, form]);

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      // Use fallback role ID of 1 if no role is selected or no roles are available
      const roleId = data.role || 1;

      const registrationData = {
        user: {
          email: data.email,
          password: data.password,
          username: data.username,
          first_name: data.first_name,
          last_name: data.last_name,
          employee_no: data.employee_no,
          branch: data.branch,
          role: roleId,
        }
      };



      await postRegistration(registrationData).unwrap();
      toast.success('Registration successful! Please login with your credentials.');
      navigate("/auth/login");
    } catch (err: any) {
      const { data: errorData } = err || {};

      // Handle field-specific errors
      if (errorData && typeof errorData === 'object') {
        const fieldErrors = [];

        // Check for general error message first
        if (errorData.error && typeof errorData.error === 'string') {
          toast.error(errorData.error);
          return;
        }

        // Check for errors in user object
        if (errorData.user && typeof errorData.user === 'object') {
          const fields = ['email', 'password', 'username', 'first_name', 'last_name', 'employee_no', 'branch', 'role'];
          for (const field of fields) {
            if (errorData.user[field] && Array.isArray(errorData.user[field])) {
              fieldErrors.push(`${field.replace('_', ' ')}: ${errorData.user[field][0]}`);
            }
          }
        }

        // Check for direct field errors (fallback)
        if (fieldErrors.length === 0) {
          const fields = ['email', 'password', 'username', 'first_name', 'last_name', 'employee_no', 'branch', 'role'];
          for (const field of fields) {
            if (errorData[field] && Array.isArray(errorData[field])) {
              fieldErrors.push(`${field.replace('_', ' ')}: ${errorData[field][0]}`);
            }
          }
        }

        if (fieldErrors.length > 0) {
          toast.error(fieldErrors[0]); // Show the first error
        } else {
          toast.error('Registration failed. Please check your information and try again.');
        }
      } else {
        toast.error('Registration failed. Please try again.');
      }
    }
  };

  return (
    <div className="w-full min-h-screen lg:w-1/2 flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-md flex flex-col items-center gap-8">
        {/* Logo */}
        <div className="flex justify-center mt-6 mb-2">
          <img src={Logo} alt="GMC Logo" className="h-16 w-auto" />
        </div>

        {/* Header */}
        <div className="text-center w-full">
          <h2 className="text-2xl font-bold text-gray-900 mb-1">Let’s Sign you up!</h2>
          <p className="text-gray-500 text-sm mb-6">
            Enter your details to create an account
          </p>
        </div>

        {/* Signup Form */}
        <form className="w-full" onSubmit={handleSubmit(onSubmit)}>

          {/* First Name */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="first_name">First Name</label>
            <input
              {...register("first_name")}
              id="first_name"
              type="text"
              placeholder="Enter your first name"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.first_name && (
              <p className="text-red-500 text-xs mt-1">{errors.first_name.message}</p>
            )}
          </div>

          {/* Last Name */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="last_name">Last Name</label>
            <input
              {...register("last_name")}
              id="last_name"
              type="text"
              placeholder="Enter your last name"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.last_name && (
              <p className="text-red-500 text-xs mt-1">{errors.last_name.message}</p>
            )}
          </div>

          {/* Username */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="username">Username</label>
            <input
              {...register("username")}
              id="username"
              type="text"
              placeholder="Enter your username"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.username && (
              <p className="text-red-500 text-xs mt-1">{errors.username.message}</p>
            )}
          </div>

          {/* Email */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="email">Email Address</label>
            <input
              {...register("email")}
              id="email"
              type="email"
              placeholder="Enter your email"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
            )}
          </div>

          {/* Employee Number */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="employee_no">Employee Number</label>
            <input
              {...register("employee_no")}
              id="employee_no"
              type="text"
              placeholder="Enter your employee number"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.employee_no && (
              <p className="text-red-500 text-xs mt-1">{errors.employee_no.message}</p>
            )}
          </div>

          {/* Branch */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="branch">Branch</label>
            <select
              {...register("branch")}
              id="branch"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value="">Select a branch</option>
              {loadingBranches ? (
                <option disabled>Loading branches...</option>
              ) : Array.isArray(branches) ? (
                branches.map((branch) => (
                  <option key={branch.id} value={branch.id?.toString()}>
                    {branch.name} ({branch.branch_code})
                  </option>
                ))
              ) : (
                <option disabled>No branches available</option>
              )}
            </select>
            {errors.branch && (
              <p className="text-red-500 text-xs mt-1">{errors.branch.message}</p>
            )}
          </div>

          {/* Role */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="role">Role</label>
            <select
              {...register("role")}
              id="role"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value="">Select a role</option>
              {loadingUserRoles ? (
                <option disabled>Loading roles...</option>
              ) : userRolesError ? (
                <option disabled>Error loading roles</option>
              ) : Array.isArray(userRoles) && userRoles.length > 0 ? (
                userRoles.map((role) => (
                  <option key={role.id} value={role.id?.toString()}>
                    {role.name}
                  </option>
                ))
              ) : (
                <>
                  <option disabled>No roles available from API</option>
                  <option value="1">Default Role (ID: 1)</option>
                </>
              )}
            </select>
            {errors.role && (
              <p className="text-red-500 text-xs mt-1">{errors.role.message}</p>
            )}

          </div>

          {/* Password */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="password">Password</label>
            <div className="relative">
              <input
                {...register("password")}
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                tabIndex={-1}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>
            )}
          </div>

          {/* Confirm Password */}
          <div className="mb-6">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="confirmPassword">Confirm Password</label>
            <div className="relative">
              <input
                {...register("confirmPassword")}
                id="confirmPassword"
                type={showConfirm ? 'text' : 'password'}
                placeholder="Enter your password"
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
              />
              <button
                type="button"
                onClick={() => setShowConfirm(!showConfirm)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                tabIndex={-1}
              >
                {showConfirm ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-500 text-xs mt-1">{errors.confirmPassword.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <button
            className="w-full bg-black text-white font-semibold py-2 rounded-md hover:bg-gray-900 transition-colors mb-2"
            type="submit"
            disabled={isSignUpLoading}
          >
            {isSignUpLoading ? (
              <span className='flex items-center justify-center gap-2'>
                <Loader className="animate-spin" size={22} />
                Signing Up...
              </span>
            ) : (
              <span>Sign Up</span>
            )}
          </button>
        </form>

        {/* Sign In Link */}
        <div className="text-center w-full">
          <span className="text-gray-500 text-sm">Already have an account?</span>{" "}
          <Link to="/auth/login" className="text-red-500 hover:underline text-sm font-medium">
            Sign In
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SignupPage;
