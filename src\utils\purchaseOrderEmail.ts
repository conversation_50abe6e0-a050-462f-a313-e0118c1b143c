import { PurchaseOrder } from "@/types/procurement";

export interface EmailTemplate {
  subject: string;
  body: string;
  isHTML?: boolean;
}

export interface EmailOptions {
  to: string[];
  cc?: string[];
  bcc?: string[];
  attachPDF?: boolean;
  customMessage?: string;
  template?: 'standard' | 'urgent' | 'revision' | 'cancellation';
}

export const generateEmailTemplate = (
  purchaseOrder: PurchaseOrder,
  template: 'standard' | 'urgent' | 'revision' | 'cancellation' = 'standard',
  customMessage?: string
): EmailTemplate => {
  const formatCurrency = (amount: number | undefined, currency: string = "USD") => {
    if (!amount) return `${currency} 0.00`;
    return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const companyName = "GMC Company";
  const companyEmail = "<EMAIL>";
  const companyPhone = "+****************";

  const templates = {
    standard: {
      subject: `Purchase Order ${purchaseOrder.po_number} - ${companyName}`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .po-details { background: #fff; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 15px 0; }
            .po-number { font-size: 18px; font-weight: bold; color: #007bff; }
            .highlight { background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
            table { width: 100%; border-collapse: collapse; margin: 15px 0; }
            th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
            th { background: #f8f9fa; font-weight: bold; }
            .text-right { text-align: right; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Purchase Order Notification</h2>
            <p>Dear ${purchaseOrder.supplier_name || 'Supplier'},</p>
            <p>We are pleased to send you the following purchase order for your review and processing.</p>
          </div>
          
          <div class="content">
            ${customMessage ? `
            <div class="highlight">
              <strong>Special Instructions:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <div class="po-details">
              <h3>Purchase Order Details</h3>
              <p><strong>PO Number:</strong> <span class="po-number">${purchaseOrder.po_number}</span></p>
              <p><strong>Date:</strong> ${formatDate(purchaseOrder.created_at)}</p>
              <p><strong>Status:</strong> ${purchaseOrder.status}</p>
              <p><strong>Expected Delivery Date:</strong> ${formatDate(purchaseOrder.delivery_date)}</p>
              <p><strong>Delivery Location:</strong> ${purchaseOrder.delivery_location_name}</p>
              ${purchaseOrder.delivery_address ? `<p><strong>Delivery Address:</strong> ${purchaseOrder.delivery_address}</p>` : ''}
              <p><strong>Payment Terms:</strong> ${purchaseOrder.payment_terms || 'As agreed'}</p>
              <p><strong>Total Value:</strong> ${formatCurrency(purchaseOrder.total_value, purchaseOrder.currency)}</p>
            </div>
            
            <h3>Order Items</h3>
            <table>
              <thead>
                <tr>
                  <th>Item</th>
                  <th>Quantity</th>
                  <th>Unit</th>
                  <th>Unit Price</th>
                  <th class="text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                ${purchaseOrder.items?.map(item => `
                  <tr>
                    <td>${item.product_name || 'N/A'}</td>
                    <td>${item.quantity || 0}</td>
                    <td>${item.unit_of_measure_name || 'N/A'}</td>
                    <td>${formatCurrency(item.unit_price, purchaseOrder.currency)}</td>
                    <td class="text-right">${formatCurrency((item.quantity || 0) * (item.unit_price || 0), purchaseOrder.currency)}</td>
                  </tr>
                `).join('') || '<tr><td colspan="5">No items</td></tr>'}
              </tbody>
            </table>
            
            <div style="text-align: right; margin-top: 15px;">
              <p><strong>Subtotal: ${formatCurrency(purchaseOrder.subtotal, purchaseOrder.currency)}</strong></p>
              ${purchaseOrder.tax_amount ? `<p><strong>Tax (${purchaseOrder.tax_rate || 0}%): ${formatCurrency(purchaseOrder.tax_amount, purchaseOrder.currency)}</strong></p>` : ''}
              <p style="font-size: 18px;"><strong>Total: ${formatCurrency(purchaseOrder.total_value, purchaseOrder.currency)}</strong></p>
            </div>
            
            ${purchaseOrder.notes ? `
            <div class="po-details">
              <h3>Notes</h3>
              <p>${purchaseOrder.notes.replace(/\n/g, '<br>')}</p>
            </div>
            ` : ''}
            
            ${purchaseOrder.terms_and_conditions ? `
            <div class="po-details">
              <h3>Terms and Conditions</h3>
              <p>${purchaseOrder.terms_and_conditions.replace(/\n/g, '<br>')}</p>
            </div>
            ` : ''}
            
            <div class="highlight">
              <p><strong>Next Steps:</strong></p>
              <ul>
                <li>Please confirm receipt of this purchase order</li>
                <li>Provide delivery confirmation and tracking information</li>
                <li>Contact us immediately if there are any issues or concerns</li>
                <li>Ensure all items are delivered by the specified delivery date</li>
              </ul>
            </div>
          </div>
          
          <div class="footer">
            <p><strong>Contact Information:</strong></p>
            <p>${companyName}<br>
            Email: ${companyEmail}<br>
            Phone: ${companyPhone}</p>
            <p><em>This is an automated message. Please do not reply directly to this email.</em></p>
          </div>
        </body>
        </html>
      `
    },
    
    urgent: {
      subject: `🚨 URGENT - Purchase Order ${purchaseOrder.po_number} - Immediate Attention Required`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .urgent-header { background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .po-details { background: #fff; border: 2px solid #dc3545; border-radius: 8px; padding: 15px; margin: 15px 0; }
            .po-number { font-size: 18px; font-weight: bold; color: #dc3545; }
            .urgent-notice { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="urgent-header">
            <h2>🚨 URGENT PURCHASE ORDER</h2>
            <p>This purchase order requires immediate attention and expedited processing.</p>
          </div>
          
          <div class="content">
            <div class="urgent-notice">
              <strong>⚠️ URGENT PROCESSING REQUIRED</strong><br>
              This purchase order has been marked as urgent and requires immediate attention. Please prioritize this order and confirm receipt within 2 hours.
            </div>
            
            ${customMessage ? `
            <div class="urgent-notice">
              <strong>Special Instructions:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <div class="po-details">
              <h3>Purchase Order Details</h3>
              <p><strong>PO Number:</strong> <span class="po-number">${purchaseOrder.po_number}</span></p>
              <p><strong>Date:</strong> ${formatDate(purchaseOrder.created_at)}</p>
              <p><strong>Expected Delivery Date:</strong> <strong style="color: #dc3545;">${formatDate(purchaseOrder.delivery_date)}</strong></p>
              <p><strong>Total Value:</strong> ${formatCurrency(purchaseOrder.total_value, purchaseOrder.currency)}</p>
            </div>
            
            <div class="urgent-notice">
              <p><strong>Immediate Action Required:</strong></p>
              <ul>
                <li>Confirm receipt within 2 hours</li>
                <li>Provide estimated delivery time immediately</li>
                <li>Contact us at ${companyPhone} for any issues</li>
                <li>Prioritize this order in your fulfillment queue</li>
              </ul>
            </div>
          </div>
          
          <div class="footer">
            <p><strong>Emergency Contact:</strong> ${companyPhone}</p>
            <p><strong>Email:</strong> ${companyEmail}</p>
          </div>
        </body>
        </html>
      `
    },
    
    revision: {
      subject: `Purchase Order ${purchaseOrder.po_number} - REVISED`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .revision-header { background: #ffc107; color: #212529; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .po-details { background: #fff; border: 2px solid #ffc107; border-radius: 8px; padding: 15px; margin: 15px 0; }
            .revision-notice { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="revision-header">
            <h2>📝 REVISED PURCHASE ORDER</h2>
            <p>This is a revised version of purchase order ${purchaseOrder.po_number}. Please review the changes carefully.</p>
          </div>
          
          <div class="content">
            <div class="revision-notice">
              <strong>⚠️ IMPORTANT: This is a revised purchase order</strong><br>
              Please disregard any previous versions of this purchase order. This revision supersedes all previous versions.
            </div>
            
            ${customMessage ? `
            <div class="revision-notice">
              <strong>Revision Notes:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <div class="po-details">
              <h3>Revised Purchase Order Details</h3>
              <p><strong>PO Number:</strong> ${purchaseOrder.po_number}</p>
              <p><strong>Revision Date:</strong> ${formatDate(purchaseOrder.updated_at || purchaseOrder.created_at)}</p>
              <p><strong>Total Value:</strong> ${formatCurrency(purchaseOrder.total_value, purchaseOrder.currency)}</p>
            </div>
          </div>
          
          <div class="footer">
            <p><strong>Contact:</strong> ${companyEmail} | ${companyPhone}</p>
          </div>
        </body>
        </html>
      `
    },
    
    cancellation: {
      subject: `Purchase Order ${purchaseOrder.po_number} - CANCELLED`,
      body: `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .cancellation-header { background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .content { padding: 0 20px; }
            .footer { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; font-size: 12px; color: #666; }
            .cancellation-notice { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="cancellation-header">
            <h2>❌ PURCHASE ORDER CANCELLED</h2>
            <p>Purchase Order ${purchaseOrder.po_number} has been cancelled.</p>
          </div>
          
          <div class="content">
            <div class="cancellation-notice">
              <strong>⚠️ ORDER CANCELLATION NOTICE</strong><br>
              This purchase order has been cancelled. Please do not proceed with fulfillment.
            </div>
            
            ${customMessage ? `
            <div class="cancellation-notice">
              <strong>Cancellation Reason:</strong><br>
              ${customMessage}
            </div>
            ` : ''}
            
            <p>If you have already begun processing this order, please contact us immediately at ${companyPhone}.</p>
          </div>
          
          <div class="footer">
            <p><strong>Contact:</strong> ${companyEmail} | ${companyPhone}</p>
          </div>
        </body>
        </html>
      `
    }
  };

  return {
    ...templates[template],
    isHTML: true
  };
};

export const createEmailData = (
  purchaseOrder: PurchaseOrder,
  options: EmailOptions
): any => {
  const template = generateEmailTemplate(
    purchaseOrder,
    options.template || 'standard',
    options.customMessage
  );

  return {
    to: options.to,
    cc: options.cc || [],
    bcc: options.bcc || [],
    subject: template.subject,
    body: template.body,
    isHTML: template.isHTML,
    attachments: options.attachPDF ? [
      {
        filename: `purchase-order-${purchaseOrder.po_number}.pdf`,
        contentType: 'application/pdf'
      }
    ] : []
  };
};

export const validateEmailAddresses = (emails: string[]): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emails.every(email => emailRegex.test(email.trim()));
};

export const getDefaultEmailRecipients = (purchaseOrder: PurchaseOrder): string[] => {
  const recipients: string[] = [];
  
  if (purchaseOrder.supplier_email) {
    recipients.push(purchaseOrder.supplier_email);
  }
  
  return recipients;
};
